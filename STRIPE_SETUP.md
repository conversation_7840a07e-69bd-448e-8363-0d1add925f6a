# Stripe Integration Setup Guide

## Environment Variables Required

Set these environment variables in your Supabase project dashboard:

### Supabase Edge Functions Environment Variables

1. **STRIPE_SECRET_KEY**
   - Your Stripe secret key (starts with `sk_`)
   - Get this from your Stripe Dashboard → Developers → API Keys

2. **STRIPE_WEBHOOK_SECRET**
   - Webhook endpoint secret for signature verification
   - Generated when you create a webhook endpoint

3. **SUPABASE_URL**
   - Your Supabase project URL
   - Automatically set by Supabase

4. **SUPABASE_SERVICE_ROLE_KEY**
   - Your Supabase service role key
   - Get this from Supabase Dashboard → Settings → API

## Stripe Webhook Configuration

1. **Create Webhook Endpoint**
   - Go to Stripe Dashboard → Developers → Webhooks
   - Click "Add endpoint"
   - URL: `https://your-project.supabase.co/functions/v1/stripeWebhook`
   - Events to send: `checkout.session.completed`

2. **Get Webhook Secret**
   - After creating the webhook, click on it
   - Copy the "Signing secret" (starts with `whsec_`)
   - Set this as `STRIPE_WEBHOOK_SECRET` in Supabase

## Testing the Integration

### 1. Test Mode
- Use Stripe test mode for development
- Test card numbers: 4242 4242 4242 4242
- Any future expiry date and any 3-digit CVC

### 2. Test Flow
1. Go to your product detail page
2. Click "Buy Now"
3. You should be redirected to Stripe Checkout
4. Complete payment with test card
5. Should redirect to `/order-confirmation`
6. Check Supabase orders table for new order

### 3. Test Webhook
- Use Stripe CLI to test webhooks locally
- Or check Stripe Dashboard → Webhooks → Recent deliveries

## Troubleshooting

### Common Issues

1. **"Missing required fields" error**
   - Check that all environment variables are set
   - Verify product data is being passed correctly

2. **Webhook not receiving events**
   - Check webhook endpoint URL is correct
   - Verify webhook secret is set correctly
   - Check Supabase Edge Function logs

3. **Orders not being created**
   - Check Supabase logs for errors
   - Verify RLS policies allow webhook to insert orders
   - Check that `SUPABASE_SERVICE_ROLE_KEY` is correct

### Debugging Steps

1. **Check Edge Function Logs**
   ```bash
   supabase functions logs stripeCheckout
   supabase functions logs stripeWebhook
   ```

2. **Test Edge Functions Locally**
   ```bash
   supabase functions serve stripeCheckout
   ```

3. **Verify Environment Variables**
   - Check Supabase Dashboard → Settings → Edge Functions
   - Ensure all required variables are set

## Production Deployment

1. **Switch to Live Mode**
   - Update `STRIPE_SECRET_KEY` to live key
   - Update webhook endpoint to production URL
   - Test with real payment methods

2. **Security Considerations**
   - Never expose secret keys in frontend code
   - Use environment variables for all secrets
   - Implement proper webhook signature verification

3. **Monitoring**
   - Set up Stripe Dashboard alerts
   - Monitor Supabase Edge Function logs
   - Track order creation success rates 
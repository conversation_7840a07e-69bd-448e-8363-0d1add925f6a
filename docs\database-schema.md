# EpicFreelancer Database Schema

## Current Implementation Status

This document reflects the **actual current database schema** as implemented in the Supabase migrations.

---

## **IMPLEMENTED TABLES** ✅

### `categories`
```sql
CREATE TABLE public.categories (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  slug text UNIQUE NOT NULL,
  description text,
  icon text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

**Sample Data:**
- Design
- Development  
- Marketing
- Tools

### `listings` (Unified Products/Services/Tools)
```sql
CREATE TABLE public.listings (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text,
  type text NOT NULL CHECK (type IN ('product', 'service', 'tool')),
  price decimal(10,2),
  pricing_type text DEFAULT 'fixed' CHECK (pricing_type IN ('fixed', 'hourly', 'monthly', 'package')),
  image_url text,
  gallery_urls text[],
  category_id uuid REFERENCES public.categories(id),
  seller_name text NOT NULL,
  rating decimal(3,2) DEFAULT 0,
  review_count integer DEFAULT 0,
  featured boolean DEFAULT false,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'suspended')),
  tags text[],
  delivery_time integer, -- in days
  revisions integer,
  features text[],
  tech_specs jsonb,
  license_type text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `reviews`
```sql
CREATE TABLE public.reviews (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) ON DELETE CASCADE NOT NULL,
  buyer_name text NOT NULL,
  rating integer CHECK (rating >= 1 AND rating <= 5) NOT NULL,
  comment text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `orders`
```sql
CREATE TABLE public.orders (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) NOT NULL,
  buyer_name text NOT NULL,
  seller_name text NOT NULL,
  amount decimal(10,2) NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'cancelled', 'refunded', 'active')),
  delivery_date timestamp with time zone,
  download_urls text[],
  notes text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

---

## **PENDING TABLES** ❌

### `site_settings` (Admin Configuration)
```sql
-- TO BE CREATED
CREATE TABLE public.site_settings (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  setting_key text UNIQUE NOT NULL,
  setting_value jsonb,
  description text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `commission_settings` (Commission Management)
```sql
-- TO BE CREATED
CREATE TABLE public.commission_settings (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  category_id uuid REFERENCES public.categories(id),
  commission_rate decimal(5,2) NOT NULL, -- percentage
  seller_tier text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `messages` (User Communication)
```sql
-- TO BE CREATED
CREATE TABLE public.messages (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id uuid REFERENCES auth.users(id) NOT NULL,
  recipient_id uuid REFERENCES auth.users(id) NOT NULL,
  listing_id uuid REFERENCES public.listings(id),
  subject text,
  content text NOT NULL,
  read_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `seller_profiles` (Seller Information)
```sql
-- TO BE CREATED
CREATE TABLE public.seller_profiles (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) UNIQUE NOT NULL,
  business_name text,
  description text,
  category text,
  contact_email text,
  phone text,
  paypal_email text,
  verified boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `homepage_featured` (Manual Featured Content)
```sql
-- TO BE CREATED
CREATE TABLE public.homepage_featured (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) NOT NULL,
  section text NOT NULL, -- 'trending', 'recent', 'top-rated'
  position integer,
  expires_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `listing_metrics` (Analytics)
```sql
-- TO BE CREATED
CREATE TABLE public.listing_metrics (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) NOT NULL,
  views integer DEFAULT 0,
  clicks integer DEFAULT 0,
  sales integer DEFAULT 0,
  date date DEFAULT CURRENT_DATE,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);
```

### `vip_listings` (Paid Promotions)
```sql
-- TO BE CREATED
CREATE TABLE public.vip_listings (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) NOT NULL,
  promotion_type text NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  amount_paid decimal(10,2),
  created_at timestamp with time zone DEFAULT now() NOT NULL
);
```

---

## **ROW LEVEL SECURITY (RLS)** ❌ PENDING

Currently, RLS policies are **not implemented**. This needs to be completed for security.

### Required RLS Policies:
1. **Users can only see their own data**
2. **Sellers can only edit their own listings**
3. **Admin can see everything**
4. **Public can view active listings**

---

## **NEXT STEPS**

1. **Create missing tables** (seller_profiles, site_settings, etc.)
2. **Implement RLS policies** for security
3. **Add proper foreign key relationships** to auth.users
4. **Create indexes** for performance
5. **Set up triggers** for updated_at timestamps

For implementation order, see **[Development Plan](development-plan.md)**.

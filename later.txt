when a link is clicked, since its a react app, it does not go on top of the screen, if i click a link in footer, it stay in the footer despite the page has changed. fix that where when i clicked a link, it should instantly moved on top... u know wha i mean..


------------

now in http://localhost:8081/seller-onboarding, i want it more streamline, can you analyse it? it seems that people always need to put same info to publish different products? i mean why asking same data despite they already posted a product eariler? you know what i mean. lets brainstorm here and advise what would be the best solution for this here please.


-----------

in footer, remove all social media links

-------------

in http://localhost:8081/order-confirmation?session_id=cs_test_a1Bi68t59LRGT1Q01kQsspocLa4lx4IQlsGlXE9vUQdQcCs5QmJNmiFTGQ, its not dynamic based on service type? because note, we have micro saas, digital products and services. So the payment success msg is seems generic for all? Also do the real owner that is providing the services get notified or not?

Ex:
Payment Successful!
Thank you for your purchase. Your order has been confirmed.

Session ID: cs_test_a1Bi68t59LRGT1Q01kQsspocLa4lx4IQlsGlXE9vUQdQcCs5QmJNmiFTGQ

What's Next?
1
Order Processing
Your order is being processed and will be available for download shortly.

2
Email Confirmation
You'll receive an email confirmation with your order details and download links.

3
Access Your Purchase
Visit your dashboard to access your downloads and track your orders.

-----------

order table is not being populated when payment is done. Because i dont see data here: http://localhost:8081/orders


----------

http://localhost:8081/orders page should have consistent header like we have in homepage please. Add footer also.
same in http://localhost:8081/profile
same in http://localhost:8081/buyer-dashboard

--------

now in http://localhost:8081/buyer-dashboard, seems when someone purchase a service, or buy a micro saas or even a digital product, it does not store anywhere in our DB? because in http://localhost:8081/buyer-dashboard, nothing show as a buyer, i dont see anything i bought.


-----------

is wishlist functional? because we have 3 type of details pages and none worked, not even in http://localhost:8081/buyer-dashboard's wishlist tab, its hardcoded there.

----------




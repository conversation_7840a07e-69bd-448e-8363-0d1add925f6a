import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Admin from "./pages/Admin";
import UserDashboard from "./pages/UserDashboard";
import SellerDashboard from "./pages/SellerDashboard";

import SellerOnboarding from "./pages/SellerOnboarding";
import HelpCenter from "./pages/HelpCenter";
import Contact from "./pages/Contact";
import SellerResources from "./pages/SellerResources";
import BuyerGuide from "./pages/BuyerGuide";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import Categories from "./pages/Categories";
import ForgotPassword from "./pages/ForgotPassword";
import HowItWorks from "./pages/HowItWorks";
import Support from "./pages/Support";
import DigitalProducts from "./pages/DigitalProducts";
import Services from "./pages/Services";
import MicroSaas from "./pages/MicroSaas";
import Design from "./pages/Design";
import Development from "./pages/Development";
import Marketing from "./pages/Marketing";
import Tools from "./pages/Tools";
import ProductDetail from "./pages/ProductDetail";
import ServiceDetail from "./pages/ServiceDetail";
import ToolDetail from "./pages/ToolDetail";
import NotFound from "./pages/NotFound";
import SearchResults from './pages/SearchResults';
import Profile from "./pages/Profile";
import Orders from "./pages/Orders";
import OrderConfirmation from "./pages/OrderConfirmation";
import OrderCancelled from "./pages/OrderCancelled";
import SlugTest from "./pages/SlugTest";
import { NotificationProvider } from '@/contexts/NotificationContext';
import MessageThread from "./pages/MessageThread";
import { SiteSettingsProvider } from '@/contexts/SiteSettingsContext';
import { MaintenanceMode } from '@/components/MaintenanceMode';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <NotificationProvider>
        <SiteSettingsProvider>
          <MaintenanceMode>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />

                  <Route path="/seller-onboarding" element={<SellerOnboarding />} />
                  
                  {/* Admin Routes */}
                  <Route path="/admin/*" element={<Admin />} />
                  
                  <Route path="/dashboard" element={<UserDashboard />} />
                  <Route path="/seller-dashboard" element={<SellerDashboard />} />
                  <Route path="/help" element={<HelpCenter />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/seller-resources" element={<SellerResources />} />
                  <Route path="/buyer-guide" element={<BuyerGuide />} />
                  <Route path="/privacy" element={<PrivacyPolicy />} />
                  <Route path="/terms" element={<TermsOfService />} />
                  <Route path="/categories" element={<Categories />} />
                  <Route path="/forgot-password" element={<ForgotPassword />} />
                  <Route path="/how-it-works" element={<HowItWorks />} />
                  <Route path="/support" element={<Support />} />
                  <Route path="/digital-products" element={<DigitalProducts />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/micro-saas" element={<MicroSaas />} />
                  <Route path="/design" element={<Design />} />
                  <Route path="/development" element={<Development />} />
                  <Route path="/marketing" element={<Marketing />} />
                  <Route path="/tools" element={<Tools />} />
                  <Route path="/product/:identifier" element={<ProductDetail />} />
                  <Route path="/service/:identifier" element={<ServiceDetail />} />
                  <Route path="/tool/:identifier" element={<ToolDetail />} />
                  <Route path="/search" element={<SearchResults />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/orders" element={<Orders />} />
                  <Route path="/order-confirmation" element={<OrderConfirmation />} />
                  <Route path="/order-cancelled" element={<OrderCancelled />} />
                  <Route path="/buyer-dashboard" element={<UserDashboard />} />
                  <Route path="/messages/:userId" element={<MessageThread />} />
                  <Route path="/slug-test" element={<SlugTest />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </TooltipProvider>
          </MaintenanceMode>
        </SiteSettingsProvider>
      </NotificationProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;

import React from 'react';
import { useSiteSetting } from '@/hooks/useSupabaseData';
import { useAuth } from '@/contexts/AuthContext';

interface MaintenanceModeProps {
  children: React.ReactNode;
}

export const MaintenanceMode: React.FC<MaintenanceModeProps> = ({ children }) => {
  const maintenanceMode = useSiteSetting('maintenance_mode');
  const { user, profile } = useAuth();

  // Allow admins to bypass maintenance mode
  const isAdmin = profile?.is_admin;
  
  if (maintenanceMode === 'true' && !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mb-6">
            <i className="fas fa-tools text-6xl text-blue-500 mb-4"></i>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              We're Under Maintenance
            </h1>
            <p className="text-gray-600">
              We're currently performing some maintenance on our site. 
              We'll be back shortly. Thank you for your patience.
            </p>
          </div>
          
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">What's happening?</h3>
              <p className="text-sm text-blue-700">
                We're updating our systems to provide you with a better experience. 
                This should only take a few minutes.
              </p>
            </div>
            
            <div className="text-sm text-gray-500">
              <p>Expected completion: Soon</p>
              <p>If you need immediate assistance, please contact our support team.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}; 
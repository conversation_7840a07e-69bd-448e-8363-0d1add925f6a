import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  Users,
  Package,
  Settings,
  BarChart3,
  FileText,
  CreditCard,
  Shield,
  Database,
  Activity
} from 'lucide-react';

interface AdminSidebarProps {
  className?: string;
}

const sidebarItems = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
  },
  {
    title: 'Content Management',
    href: '/admin/content',
    icon: Package,
  },
  {
    title: 'Financial',
    href: '/admin/financial',
    icon: CreditCard,
  },
  {
    title: 'Site Settings',
    href: '/admin/settings',
    icon: Settings,
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
  },
  {
    title: 'Reports',
    href: '/admin/reports',
    icon: FileText,
  },
  {
    title: 'Security',
    href: '/admin/security',
    icon: Shield,
  },
  {
    title: 'System',
    href: '/admin/system',
    icon: Database,
  },
  {
    title: 'Activity Log',
    href: '/admin/activity',
    icon: Activity,
  },
];

export default function AdminSidebar({ className }: AdminSidebarProps) {
  const location = useLocation();

  return (
    <div className={cn('pb-12', className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            Admin Panel
          </h2>
          <div className="space-y-1">
            {sidebarItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              
              return (
                <Link key={item.href} to={item.href}>
                  <Button
                    variant={isActive ? 'secondary' : 'ghost'}
                    className={cn(
                      'w-full justify-start',
                      isActive && 'bg-secondary'
                    )}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {item.title}
                  </Button>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

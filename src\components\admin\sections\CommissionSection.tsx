import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";

export const CommissionSection = () => (
  <div className="space-y-6">
    <Card>
      <CardHeader>
        <CardTitle>Global Commission Rates</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="global-rate">Global Rate (%)</Label>
            <Input id="global-rate" type="number" defaultValue="10" />
          </div>
          <div>
            <Label htmlFor="minimum-commission">Minimum Commission ($)</Label>
            <Input id="minimum-commission" type="number" defaultValue="5" />
          </div>
          <div>
            <Label htmlFor="maximum-commission">Maximum Commission ($)</Label>
            <Input id="maximum-commission" type="number" defaultValue="500" />
          </div>
        </div>
        <Button>Update Global Rates</Button>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Category-Specific Rates</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[
            { category: 'Digital Products', rate: 8 },
            { category: 'Services', rate: 12 },
            { category: 'Micro SaaS', rate: 15 }
          ].map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="font-medium">{item.category}</span>
              <div className="flex items-center space-x-2">
                <Input type="number" defaultValue={item.rate} className="w-20" />
                <span>%</span>
                <Button size="sm">Update</Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Seller Tier Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[
            { tier: 'Bronze', minSales: 0, commission: 12 },
            { tier: 'Silver', minSales: 50, commission: 10 },
            { tier: 'Gold', minSales: 200, commission: 8 },
            { tier: 'Platinum', minSales: 500, commission: 6 }
          ].map((tier, index) => (
            <div key={index} className="grid grid-cols-4 gap-4 items-center">
              <Badge>{tier.tier}</Badge>
              <div>
                <Label>Min Sales</Label>
                <Input type="number" defaultValue={tier.minSales} />
              </div>
              <div>
                <Label>Commission %</Label>
                <Input type="number" defaultValue={tier.commission} />
              </div>
              <Button size="sm">Update</Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

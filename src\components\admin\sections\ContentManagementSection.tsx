import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAllListings } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export default function ContentManagementSection() {
  const { data: listings, isLoading, refetch } = useAllListings();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const handleApproveListing = async (listingId: string) => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ 
          status: 'active',
          approved_at: new Date().toISOString()
        })
        .eq('id', listingId);

      if (error) throw error;

      toast({
        title: "Listing Approved",
        description: "The listing has been approved and is now live.",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve listing. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRejectListing = async (listingId: string) => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ 
          status: 'rejected',
          rejected_at: new Date().toISOString()
        })
        .eq('id', listingId);

      if (error) throw error;

      toast({
        title: "Listing Rejected",
        description: "The listing has been rejected.",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject listing. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFeatureListing = async (listingId: string, featured: boolean) => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ featured })
        .eq('id', listingId);

      if (error) throw error;

      toast({
        title: featured ? "Listing Featured" : "Listing Unfeatured",
        description: featured 
          ? "The listing is now featured on the homepage." 
          : "The listing is no longer featured.",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update listing. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredListings = listings?.filter(listing => {
    const matchesSearch = listing.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         listing.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         listing.profiles?.full_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || listing.status === statusFilter;
    const matchesType = typeFilter === 'all' || listing.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  }) || [];

  const totalPages = Math.ceil(filteredListings.length / pageSize);
  const paginatedListings = filteredListings.slice((page - 1) * pageSize, page * pageSize);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Content Management</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Loading listings...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalListings = listings?.length || 0;
  const activeListings = listings?.filter(listing => listing.status === 'active').length || 0;
  const pendingListings = listings?.filter(listing => listing.status === 'pending').length || 0;
  const rejectedListings = listings?.filter(listing => listing.status === 'rejected').length || 0;
  const featuredListings = listings?.filter(listing => listing.featured).length || 0;

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Listings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalListings}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeListings}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingListings}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rejectedListings}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{featuredListings}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Content Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by title, description, or seller..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="product">Product</SelectItem>
                <SelectItem value="service">Service</SelectItem>
                <SelectItem value="tool">Tool</SelectItem>
                <SelectItem value="microsaas">Micro SaaS</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Listings List */}
          <div className="space-y-4">
            {paginatedListings.map((listing) => (
              <div key={listing.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    {listing.image_url ? (
                      <img 
                        src={listing.image_url} 
                        alt={listing.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <span className="text-sm text-gray-500">No Image</span>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{listing.title}</p>
                    <p className="text-sm text-muted-foreground">
                      by {listing.profiles?.full_name || 'Unknown Seller'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {listing.categories?.name} • ${listing.price}
                    </p>
                    <div className="flex gap-2 mt-1">
                      <Badge variant="outline">{listing.type}</Badge>
                      <Badge variant={
                        listing.status === 'active' ? 'default' :
                        listing.status === 'pending' ? 'secondary' :
                        'destructive'
                      }>
                        {listing.status}
                      </Badge>
                      {listing.featured && (
                        <Badge variant="default">Featured</Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {listing.status === 'pending' && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleApproveListing(listing.id)}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleRejectListing(listing.id)}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                  {listing.status === 'active' && (
                    <>
                      <Button
                        size="sm"
                        variant={listing.featured ? "destructive" : "outline"}
                        onClick={() => handleFeatureListing(listing.id, !listing.featured)}
                      >
                        {listing.featured ? 'Unfeature' : 'Feature'}
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleRejectListing(listing.id)}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                  {listing.status === 'rejected' && (
                    <Button
                      size="sm"
                      onClick={() => handleApproveListing(listing.id)}
                    >
                      Approve
                    </Button>
                  )}
                </div>
              </div>
            ))}
            {paginatedListings.length === 0 && (
              <p className="text-center text-muted-foreground py-8">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' 
                  ? 'No listings match your filters' 
                  : 'No listings found'}
              </p>
            )}
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-8">
              <Button size="sm" variant="outline" onClick={() => setPage(page - 1)} disabled={page === 1}>
                Previous
              </Button>
              <span className="mx-2 text-sm">Page {page} of {totalPages}</span>
              <Button size="sm" variant="outline" onClick={() => setPage(page + 1)} disabled={page === totalPages}>
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAllUsers, useAllListings, useAllOrders, useCommissionStats } from '@/hooks/useSupabaseData';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';

export default function DashboardSection() {
  const { data: users, isLoading: usersLoading } = useAllUsers();
  const { data: listings, isLoading: listingsLoading } = useAllListings();
  const { data: orders, isLoading: ordersLoading } = useAllOrders(10);
  const { data: stats, isLoading: statsLoading } = useCommissionStats();

  const isLoading = usersLoading || listingsLoading || ordersLoading || statsLoading;

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Stats Cards Skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24 mb-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20 mb-1" />
                <Skeleton className="h-3 w-28" />
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Commission/Listing Status Skeleton */}
        <div className="grid gap-4 md:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-32 mb-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-40 mb-2" />
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-4 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Charts Skeleton */}
        <div className="grid gap-4 md:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-40 mb-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-40 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Recent Orders Skeleton */}
        <Card>
          <CardHeader>
            <Skeleton className="h-4 w-32 mb-2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="w-10 h-10 rounded-lg" />
                    <div>
                      <Skeleton className="h-4 w-32 mb-2" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                  </div>
                  <div className="text-right">
                    <Skeleton className="h-4 w-12 mb-2" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalUsers = users?.length || 0;
  const totalListings = listings?.length || 0;
  const totalOrders = stats?.totalOrders || 0;
  const totalRevenue = stats?.totalRevenue || 0;
  const totalCommission = stats?.totalCommission || 0;
  const monthlyRevenue = stats?.monthlyRevenue || 0;
  const monthlyCommission = stats?.monthlyCommission || 0;

  const activeListings = listings?.filter(listing => listing.status === 'active').length || 0;
  const pendingListings = listings?.filter(listing => listing.status === 'pending').length || 0;
  const sellers = users?.filter(user => user.is_seller).length || 0;
  const buyers = totalUsers - sellers;
  const rejectedListings = listings?.filter(listing => listing.status === 'rejected').length || 0;

  const pieData = [
    { name: 'Active', value: activeListings },
    { name: 'Pending', value: pendingListings },
    { name: 'Rejected', value: rejectedListings },
  ];
  const COLORS = ['#82ca9d', '#8884d8', '#ff6b6b'];
  const barData = [
    { month: 'This Month', Revenue: monthlyRevenue, Commission: monthlyCommission },
    { month: 'Total', Revenue: totalRevenue, Commission: totalCommission },
  ];

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Badge variant="secondary">{buyers} buyers, {sellers} sellers</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {sellers} sellers, {buyers} buyers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Listings</CardTitle>
            <Badge variant="secondary">{activeListings} active</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalListings}</div>
            <p className="text-xs text-muted-foreground">
              {activeListings} active, {pendingListings} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <Badge variant="secondary">Completed</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.monthlyOrders || 0} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <Badge variant="secondary">Platform</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              ${monthlyRevenue.toFixed(2)} this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Commission Stats */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Commission Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Total Commission</span>
              <span className="font-bold">${totalCommission.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Monthly Commission</span>
              <span className="font-bold">${monthlyCommission.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Commission Rate</span>
              <span className="font-bold">10%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Listing Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Active Listings</span>
              <Badge variant="default">{activeListings}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Pending Approval</span>
              <Badge variant="secondary">{pendingListings}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Rejected</span>
              <Badge variant="destructive">
                {rejectedListings}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Revenue & Commission Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue & Commission</CardTitle>
          </CardHeader>
          <CardContent style={{ height: 260 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="Revenue" fill="#8884d8" radius={[8, 8, 0, 0]} />
                <Bar dataKey="Commission" fill="#82ca9d" radius={[8, 8, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        {/* Listing Status Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Listing Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent style={{ height: 260 }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={70}
                  label
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {orders?.slice(0, 5).map((order) => (
              <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-medium">${order.amount}</span>
                  </div>
                  <div>
                    <p className="font-medium">{order.listings?.title || 'Unknown Product'}</p>
                    <p className="text-sm text-muted-foreground">
                      {order.profiles?.full_name || 'Unknown Buyer'} → {order.profiles?.full_name || 'Unknown Seller'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">${order.amount}</p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(order.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
            {(!orders || orders.length === 0) && (
              <p className="text-center text-muted-foreground py-8">No orders found</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

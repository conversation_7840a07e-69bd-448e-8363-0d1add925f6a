import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAllUsers } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export default function UserManagementSection() {
  const { data: users, isLoading, refetch } = useAllUsers();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');

  const handleApproveSeller = async (userId: string) => {
    try {
      console.log(">>>>>>>>>>> userId", userId)
      const { error } = await supabase
        .from('profiles')
        .update({ 
          is_seller: true, 
          seller_approved: true,
          seller_approved_at: new Date().toISOString()
        })
        .eq('user_id', userId); // FIXED: use user_id

      if (error) throw error;

      toast({
        title: "Seller Approved",
        description: "The seller has been approved successfully.",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve seller. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRejectSeller = async (userId: string) => {
    console.log(">>>>>>>>>>>> userId >>>>>>>>", userId)
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ 
          is_seller: false, 
          seller_approved: false,
          seller_rejected_at: new Date().toISOString()
        })
        .eq('user_id', userId); // FIXED: use user_id

      if (error) throw error;

      toast({
        title: "Seller Rejected",
        description: "The seller has been rejected.",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject seller. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredUsers = users?.filter(user => {
    const matchesSearch = user.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'approved' && user.seller_approved) ||
                         (statusFilter === 'pending' && user.is_seller && !user.seller_approved) ||
                         (statusFilter === 'rejected' && user.seller_rejected_at);
    
    const matchesRole = roleFilter === 'all' ||
                       (roleFilter === 'seller' && user.is_seller) ||
                       (roleFilter === 'buyer' && !user.is_seller);

    return matchesSearch && matchesStatus && matchesRole;
  }) || [];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Loading users...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalUsers = users?.length || 0;
  const sellers = users?.filter(user => user.is_seller).length || 0;
  const buyers = totalUsers - sellers;
  const pendingSellers = users?.filter(user => user.is_seller && !user.seller_approved).length || 0;

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sellers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sellers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Buyers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{buyers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Sellers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingSellers}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="seller">Sellers</SelectItem>
                <SelectItem value="buyer">Buyers</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users List */}
          <div className="space-y-4">
            {filteredUsers.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {user.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{user.full_name || 'Unknown User'}</p>
                    {/* <p className="text-sm text-muted-foreground">{user.email  || 'No email'}</p> */}
                    <div className="flex gap-2 mt-1">
                      {user.is_seller && (
                        <Badge variant="default">Seller</Badge>
                      )}
                      {!user.is_seller && (
                        <Badge variant="secondary">Buyer</Badge>
                      )}
                      {user.seller_approved && (
                        <Badge variant="default">Approved</Badge>
                      )}
                      {user.is_seller && !user.seller_approved && (
                        <Badge variant="secondary">Pending</Badge>
                      )}
                      {user.seller_rejected_at && (
                        <Badge variant="destructive">Rejected</Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {user.is_seller && !user.seller_approved && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleApproveSeller(user.user_id)}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleRejectSeller(user.user_id)}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                  {user.seller_approved && (
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleRejectSeller(user.user_id)}
                    >
                      Revoke
                    </Button>
                  )}
                </div>
              </div>
            ))}
            {filteredUsers.length === 0 && (
              <p className="text-center text-muted-foreground py-8">
                {searchTerm || statusFilter !== 'all' || roleFilter !== 'all' 
                  ? 'No users match your filters' 
                  : 'No users found'}
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { DollarSign, ShoppingCart, Star } from "lucide-react";

interface Analytics {
  totalEarnings: number;
  thisMonth: number;
  totalSales: number;
  activeListing: number;
  averageRating: number;
  profileViews: number;
}

interface AnalyticsMetricsProps {
  analytics: Analytics;
}

export const AnalyticsMetrics = ({ analytics }: AnalyticsMetricsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${analytics.totalEarnings.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            +${analytics.thisMonth} this month
          </p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
          <ShoppingCart className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{analytics.totalSales}</div>
          <p className="text-xs text-muted-foreground">
            {analytics.activeListing} active listings
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
          <Star className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{analytics.averageRating}</div>
          <p className="text-xs text-muted-foreground">
            {analytics.profileViews} profile views
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileUpload } from '@/components/ui/file-upload';
import { useCategories } from '@/hooks/useSupabaseData';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { generateSlug } from '@/lib/utils';

interface ListingFormData {
  title: string;
  description: string;
  price: string;
  categoryId: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  imageFiles: File[];
  digitalProductFiles: File[];
}

const initialForm: ListingFormData = {
  title: '',
  description: '',
  price: '',
  categoryId: '',
  status: 'pending',
  imageFiles: [],
  digitalProductFiles: [],
};

export default function ListingCreateForm() {
  const [form, setForm] = useState<ListingFormData>(initialForm);
  const [errors, setErrors] = useState<{ [k: string]: string }>({});
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const { data: categories = [] } = useCategories();
  const { user } = useAuth();
  const { toast } = useToast();

  const validate = () => {
    const newErrors: typeof errors = {};
    if (!form.title) newErrors.title = 'Title is required.';
    if (!form.description) newErrors.description = 'Description is required.';
    if (!form.price || isNaN(Number(form.price)) || Number(form.price) <= 0) newErrors.price = 'Enter a valid price.';
    if (!form.categoryId) newErrors.categoryId = 'Category is required.';
    if (!form.imageFiles.length) newErrors.imageFiles = 'At least one image is required.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field: keyof ListingFormData, value: any) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageFilesSelected = (files: File[]) => {
    handleChange('imageFiles', files);
  };

  const handleDigitalProductFilesSelected = (files: File[]) => {
    handleChange('digitalProductFiles', files);
  };

  const removeImageFile = (index: number) => {
    setForm((prev) => ({
      ...prev,
      imageFiles: prev.imageFiles.filter((_, i) => i !== index)
    }));
  };

  const removeDigitalProductFile = (index: number) => {
    setForm((prev) => ({
      ...prev,
      digitalProductFiles: prev.digitalProductFiles.filter((_, i) => i !== index)
    }));
  };

  const uploadImages = async (files: File[]): Promise<string[]> => {
    if (!user) return [];
    const urls: string[] = [];
    for (const file of files) {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}/${Date.now()}-${Math.random().toString(36).slice(2)}.${fileExt}`;
      const { error: uploadError } = await supabase.storage.from('listing-images').upload(filePath, file);
      if (uploadError) {
        setErrors({ submit: uploadError.message });
        return [];
      }

      const { data } = supabase.storage.from('listing-images').getPublicUrl(filePath);
      if (data?.publicUrl) urls.push(data.publicUrl);
    }
    return urls;
  };

  const uploadDigitalProductFiles = async (files: File[]): Promise<Array<{name: string, url: string, size: number, type: string}>> => {
    if (!user) return [];
    const uploadedFiles: Array<{name: string, url: string, size: number, type: string}> = [];
    
    for (const file of files) {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}/${Date.now()}-${Math.random().toString(36).slice(2)}.${fileExt}`;

      
      const { error: uploadError } = await supabase.storage.from('digital-products').upload(filePath, file);
      if (uploadError) {
        setErrors({ submit: `Failed to upload ${file.name}: ${uploadError.message}` });
        return [];
      }


      // Get signed URL for secure access
      const { data: signedUrlData } = await supabase.storage
        .from('digital-products')
        .createSignedUrl(filePath, 60 * 60 * 24 * 365); // 1 year expiry

      if (signedUrlData?.signedUrl) {
        uploadedFiles.push({
          name: file.name,
          url: signedUrlData.signedUrl,
          size: file.size,
          type: file.type
        });
      }
    }
    return uploadedFiles;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate() || !user) return;
    setSubmitting(true);
    setSuccess(false);
    setErrors({});



    let galleryUrls: string[] = [];
    if (form.imageFiles.length) {
      galleryUrls = await uploadImages(form.imageFiles);
      if (!galleryUrls.length) {
        setSubmitting(false);
        return;
      }
    }




    let digitalProductFiles: Array<{name: string, url: string, size: number, type: string}> = [];
    if (form.digitalProductFiles.length) {
      digitalProductFiles = await uploadDigitalProductFiles(form.digitalProductFiles);
      if (form.digitalProductFiles.length > 0 && !digitalProductFiles.length) {
        setSubmitting(false);
        return;
      }
    }




    const { error } = await supabase.from('listings').insert([
      {
        title: form.title,
        slug: generateSlug(form.title),
        description: form.description,
        price: parseFloat(form.price),
        category_id: form.categoryId,
        seller_id: user.id,
        seller_name: user.user_metadata.full_name,
        type: 'product',
        status: form.status,
        image_url: galleryUrls[0] || null,
        gallery_urls: galleryUrls,
        created_at: new Date().toISOString(),
      },
    ]);

    if (error) {
      setErrors({ submit: error.message });
      setSubmitting(false);
      return;
    }

    // If digital product files were uploaded, create download records
    if (digitalProductFiles.length > 0) {
      // Get the listing ID that was just created
      const { data: listingData } = await supabase
        .from('listings')
        .select('id')
        .eq('title', form.title)
        .eq('seller_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1);

      if (listingData && listingData[0]) {
        const listingId = listingData[0].id;
        

        // Create download records for each file
        for (const file of digitalProductFiles) {
          await supabase.from('downloads').insert([
            {
              order_id: null, // Will be set when order is created
              listing_id: listingId,
              user_name: user.user_metadata?.full_name || user.email, // Seller's name
              file_name: file.name,
              file_type: file.type,
              file_size: file.size,
              download_url: file.url,
              download_count: 0,
            },
          ]);
        }
      }
    }

    setSubmitting(false);
    setSuccess(true);
    setForm(initialForm);
    
    toast({
      title: "Success!",
      description: "Listing created successfully!",
      variant: "default",
    });
    
    // Close the form by triggering a custom event
    window.dispatchEvent(new CustomEvent('closeListingForm'));
  };

  return (
    <form className="w-full mx-auto max-h-[80vh] overflow-y-auto p-6 bg-white rounded shadow" onSubmit={handleSubmit}>
      <h2 className="text-2xl font-bold mb-4">Create New Listing</h2>
      <div className="mb-4">
        <label className="block font-medium mb-1">Title *</label>
        <Input
          value={form.title}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="e.g., Professional Logo Design"
        />
        {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Description *</label>
        <Textarea
          value={form.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Describe your product or service..."
          rows={4}
        />
        {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Price (USD) *</label>
        <Input
          type="number"
          value={form.price}
          onChange={(e) => handleChange('price', e.target.value)}
          placeholder="99"
          min="1"
        />
        {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Category *</label>
        <Select value={form.categoryId} onValueChange={(value) => handleChange('categoryId', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.categoryId && <p className="text-red-500 text-xs mt-1">{errors.categoryId}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Images *</label>
        <FileUpload
          onFilesSelected={handleImageFilesSelected}
          acceptedTypes="image/*"
          multiple={true}
          maxFiles={10}
          maxSize={5}
          placeholder="Upload listing images (JPG, PNG, etc.)"
          files={form.imageFiles}
          onRemoveFile={removeImageFile}
        />
        {errors.imageFiles && <p className="text-red-500 text-xs mt-1">{errors.imageFiles}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Digital Product Files (Optional)</label>
        <FileUpload
          onFilesSelected={handleDigitalProductFilesSelected}
          acceptedTypes=".pdf,.zip,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.json,.css,.html,.js,.mp3,.wav,.mp4,.webm"
          multiple={true}
          maxFiles={20}
          maxSize={50}
          placeholder="Upload files that buyers will receive"
          files={form.digitalProductFiles}
          onRemoveFile={removeDigitalProductFile}
        />
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Status</label>
        <Select value={form.status} onValueChange={(value) => handleChange('status', value as ListingFormData['status'])}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>
      </div>
             {errors.submit && <p className="text-red-500 text-xs mb-2">{errors.submit}</p>}
      <Button type="submit" disabled={submitting} className="w-full">
        {submitting ? 'Submitting...' : 'Create Listing'}
      </Button>
    </form>
  );
} 
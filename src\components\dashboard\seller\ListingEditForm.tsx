import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCategories } from '@/hooks/useSupabaseData';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { generateSlug } from '@/lib/utils';

interface ListingEditFormProps {
  listing: any;
  onUpdated?: () => void;
}

export default function ListingEditForm({ listing, onUpdated }: ListingEditFormProps) {
  const [form, setForm] = useState({
    title: '',
    description: '',
    price: '',
    categoryId: '',
    status: 'draft',
    imageFiles: [] as File[],
    galleryUrls: [] as string[],
    digitalProductFiles: [] as File[],
  });
  const [errors, setErrors] = useState<{ [k: string]: string }>({});
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const { data: categories = [] } = useCategories();
  const { user } = useAuth();

  useEffect(() => {
    if (listing) {
      setForm({
        title: listing.title || '',
        description: listing.description || '',
        price: listing.price?.toString() || '',
        categoryId: listing.category_id || '',
        status: listing.status || 'draft',
        imageFiles: [],
        galleryUrls: listing.gallery_urls || (listing.image_url ? [listing.image_url] : []),
        digitalProductFiles: [],
      });
    }
  }, [listing]);

  const validate = () => {
    const newErrors: typeof errors = {};
    if (!form.title) newErrors.title = 'Title is required.';
    if (!form.description) newErrors.description = 'Description is required.';
    if (!form.price || isNaN(Number(form.price)) || Number(form.price) <= 0) newErrors.price = 'Enter a valid price.';
    if (!form.categoryId) newErrors.categoryId = 'Category is required.';
    if (!form.galleryUrls.length && !form.imageFiles.length) newErrors.imageFiles = 'At least one image is required.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field: string, value: any) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleChange('imageFiles', Array.from(e.target.files));
    }
  };

  const handleDigitalProductFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleChange('digitalProductFiles', Array.from(e.target.files));
    }
  };

  const handleRemoveImage = (url: string) => {
    setForm((prev) => ({ ...prev, galleryUrls: prev.galleryUrls.filter((u) => u !== url) }));
  };

  const uploadImages = async (files: File[]): Promise<string[]> => {
    if (!user) return [];
    const urls: string[] = [];
    for (const file of files) {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}/${Date.now()}-${Math.random().toString(36).slice(2)}.${fileExt}`;
      const { error: uploadError } = await supabase.storage.from('listing-images').upload(filePath, file);
      if (uploadError) {
        setErrors({ submit: uploadError.message });
        return [];
      }
      const { data } = supabase.storage.from('listing-images').getPublicUrl(filePath);
      if (data?.publicUrl) urls.push(data.publicUrl);
    }
    return urls;
  };

  const uploadDigitalProductFiles = async (files: File[]): Promise<Array<{name: string, url: string, size: number, type: string}>> => {
    if (!user) return [];
    const uploadedFiles: Array<{name: string, url: string, size: number, type: string}> = [];
    
    for (const file of files) {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}/${Date.now()}-${Math.random().toString(36).slice(2)}.${fileExt}`;
      
      const { error: uploadError } = await supabase.storage.from('digital-products').upload(filePath, file);
      if (uploadError) {
        setErrors({ submit: `Failed to upload ${file.name}: ${uploadError.message}` });
        return [];
      }

      // Get signed URL for secure access
      const { data: signedUrlData } = await supabase.storage
        .from('digital-products')
        .createSignedUrl(filePath, 60 * 60 * 24 * 365); // 1 year expiry

      if (signedUrlData?.signedUrl) {
        uploadedFiles.push({
          name: file.name,
          url: signedUrlData.signedUrl,
          size: file.size,
          type: file.type
        });
      }
    }
    return uploadedFiles;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate() || !user) return;
    setSubmitting(true);
    setSuccess(false);
    setErrors({});

    let galleryUrls = [...form.galleryUrls];
    if (form.imageFiles.length) {
      const uploaded = await uploadImages(form.imageFiles);
      if (!uploaded.length) {
        setSubmitting(false);
        return;
      }
      galleryUrls = [...galleryUrls, ...uploaded];
    }

    let digitalProductFiles: Array<{name: string, url: string, size: number, type: string}> = [];
    if (form.digitalProductFiles.length) {
      digitalProductFiles = await uploadDigitalProductFiles(form.digitalProductFiles);
      if (form.digitalProductFiles.length > 0 && !digitalProductFiles.length) {
        setSubmitting(false);
        return;
      }
    }

    const { error } = await supabase.from('listings').update({
      title: form.title,
      slug: generateSlug(form.title),
      description: form.description,
      price: parseFloat(form.price),
      category_id: form.categoryId,
      status: form.status,
      image_url: galleryUrls[0] || null,
      gallery_urls: galleryUrls,
      updated_at: new Date().toISOString(),
    }).eq('id', listing.id);

    if (error) {
      setErrors({ submit: error.message });
      setSubmitting(false);
      return;
    }

    // If digital product files were uploaded, create download records
    if (digitalProductFiles.length > 0) {
      // Create download records for each file
      for (const file of digitalProductFiles) {
        await supabase.from('downloads').insert([
          {
            order_id: null, // Will be set when order is created
            listing_id: listing.id,
            user_name: user.email,
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            download_url: file.url,
            download_count: 0,
          },
        ]);
      }
    }

    setSubmitting(false);
    setSuccess(true);
    setForm((prev) => ({ ...prev, imageFiles: [], digitalProductFiles: [] }));
    if (onUpdated) onUpdated();
  };

  return (
    <form className="w-full mx-auto p-6 bg-white rounded shadow" onSubmit={handleSubmit}>
      <h2 className="text-2xl font-bold mb-4">Edit Listing</h2>
      <div className="mb-4">
        <label className="block font-medium mb-1">Title *</label>
        <Input
          value={form.title}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="e.g., Professional Logo Design"
        />
        {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Description *</label>
        <Textarea
          value={form.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Describe your product or service..."
          rows={4}
        />
        {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Price (USD) *</label>
        <Input
          type="number"
          value={form.price}
          onChange={(e) => handleChange('price', e.target.value)}
          placeholder="99"
          min="1"
        />
        {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Category *</label>
        <Select value={form.categoryId} onValueChange={(value) => handleChange('categoryId', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.categoryId && <p className="text-red-500 text-xs mt-1">{errors.categoryId}</p>}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Images *</label>
        <Input type="file" accept="image/*" multiple onChange={handleFileChange} />
        {errors.imageFiles && <p className="text-red-500 text-xs mt-1">{errors.imageFiles}</p>}
        {(form.galleryUrls.length > 0 || form.imageFiles.length > 0) && (
          <div className="flex flex-wrap gap-2 mt-2">
            {form.galleryUrls.map((url, idx) => (
              <div key={idx} className="relative group">
                <img src={url} alt={`gallery-${idx}`} className="w-20 h-20 object-cover rounded border" />
                <button type="button" onClick={() => handleRemoveImage(url)} className="absolute top-0 right-0 bg-white bg-opacity-80 rounded-full p-1 text-xs text-red-600 group-hover:visible invisible">&times;</button>
              </div>
            ))}
            {form.imageFiles.map((file, idx) => (
              <img
                key={idx}
                src={URL.createObjectURL(file)}
                alt={`preview-${idx}`}
                className="w-20 h-20 object-cover rounded border"
              />
            ))}
          </div>
        )}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Digital Product Files (Optional)</label>
        <Input 
          type="file" 
          accept=".pdf,.zip,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.json,.css,.html,.js,.mp3,.wav,.mp4,.webm" 
          multiple 
          onChange={handleDigitalProductFileChange} 
        />
        <p className="text-xs text-gray-500 mt-1">
          Upload files that buyers will receive (PDF, ZIP, documents, etc.)
        </p>
        {form.digitalProductFiles.length > 0 && (
          <div className="mt-2 space-y-1">
            {form.digitalProductFiles.map((file, idx) => (
              <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">{file.name}</span>
                <span className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="mb-4">
        <label className="block font-medium mb-1">Status</label>
        <Select value={form.status} onValueChange={(value) => handleChange('status', value as 'draft' | 'active')}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="active">Active</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {errors.submit && <p className="text-red-500 text-xs mb-2">{errors.submit}</p>}
      {success && <p className="text-green-600 text-sm mb-2">Listing updated successfully!</p>}
      <Button type="submit" disabled={submitting} className="w-full">
        {submitting ? 'Saving...' : 'Save Changes'}
      </Button>
    </form>
  );
} 
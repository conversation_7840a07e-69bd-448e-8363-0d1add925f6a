import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Edit } from "lucide-react";
import React, { useState } from 'react';
import { Link } from 'react-router-dom';

interface Product {
  id: string;
  title: string;
  price: string;
  sales: number;
  revenue: string;
  status: string;
  views: number;
  conversion: string;
}

interface ProductsManagementProps {
  products: Product[];
  onEdit?: (product: Product) => void;
}

export const ProductsManagement = ({ products, onEdit }: ProductsManagementProps) => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const allSelected = products.length > 0 && selectedIds.length === products.length;
  const toggleSelectAll = () => {
    setSelectedIds(allSelected ? [] : products.map((p) => p.id));
  };
  const toggleSelect = (id: string) => {
    setSelectedIds((prev) => prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]);
  };

  // Placeholder handlers for bulk actions
  const handleBulkActivate = () => {
    // TODO: Implement backend call
    alert('Bulk activate: ' + selectedIds.join(', '));
  };
  const handleBulkDeactivate = () => {
    // TODO: Implement backend call
    alert('Bulk deactivate: ' + selectedIds.join(', '));
  };
  const handleBulkDelete = () => {
    // TODO: Implement backend call
    alert('Bulk delete: ' + selectedIds.join(', '));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'paused':
        return <Badge className="bg-gray-500">Paused</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div>
      <div className="flex items-center mb-2 gap-2">
        <input type="checkbox" checked={allSelected} onChange={toggleSelectAll} />
        <span className="text-sm text-gray-700">Select All</span>
        <button className="ml-4 px-2 py-1 bg-green-100 text-green-700 rounded" disabled={selectedIds.length === 0} onClick={handleBulkActivate}>Activate</button>
        <button className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-700 rounded" disabled={selectedIds.length === 0} onClick={handleBulkDeactivate}>Deactivate</button>
        <button className="ml-2 px-2 py-1 bg-red-100 text-red-700 rounded" disabled={selectedIds.length === 0} onClick={handleBulkDelete}>Delete</button>
      </div>
      <div className="grid gap-4">
        {products.map((product) => (
          <Card key={product.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <input type="checkbox" checked={selectedIds.includes(product.id)} onChange={() => toggleSelect(product.id)} />
                    <div>
                      <h3 className="font-semibold text-lg">{product.title}</h3>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="font-semibold text-green-600">{product.price}</span>
                        <span className="text-sm text-gray-600">{product.sales} sales</span>
                        <span className="text-sm text-gray-600">{product.views} views</span>
                        <span className="text-sm text-gray-600">{product.conversion} conversion</span>
                      </div>
                      <div className="mt-2">
                        {getStatusBadge(product.status)}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold">{product.revenue}</p>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                </div>
                <div className="flex space-x-2">
                  <Link to={`/product/${product.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" onClick={() => onEdit && onEdit(product)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

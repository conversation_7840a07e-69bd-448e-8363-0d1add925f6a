import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface DashboardHeaderProps {
  title: string;
  userInitials?: string;
}

export const DashboardHeader = ({ title, userInitials = "JD" }: DashboardHeaderProps) => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <div className="bg-gradient-primary text-white p-2 rounded-lg mr-3">
                <i className="fas fa-rocket text-xl"></i>
              </div>
              <span className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                EpicFreelancer
              </span>
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" asChild>
              <Link to="/">Back to Marketplace</Link>
            </Button>
            <Avatar>
              <AvatarFallback>{userInitials}</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </header>
  );
};

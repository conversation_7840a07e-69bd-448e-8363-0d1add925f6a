import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, File, FileText, Image, Music, Video, Archive } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface DownloadItem {
  id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  download_url: string;
  download_count: number;
  last_download: string | null;
  created_at: string;
  order_id: string | null;
}

export default function DownloadsList() {
  const [downloads, setDownloads] = useState<DownloadItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchDownloads();
    }
  }, [user]);

  const fetchDownloads = async () => {
    if (!user) return;

    try {
      // First, get all orders for this user
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select('id')
        .eq('buyer_id', user.id);

      if (ordersError) {
        console.error('Error fetching orders:', ordersError);
        return;
      }

      if (!orders || orders.length === 0) {
        setDownloads([]);
        setLoading(false);
        return;
      }

      // Get download IDs from the orders
      const orderIds = orders.map(order => order.id);

      // Fetch downloads for these orders
      const { data, error } = await supabase
        .from('downloads')
        .select('*')
        .in('order_id', orderIds)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching downloads:', error);
        return;
      }

      console.log('Fetched downloads:', data);
      console.log('User orders:', orders);
      setDownloads(data || []);
    } catch (error) {
      console.error('Error fetching downloads:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (download: DownloadItem) => {
    setDownloading(download.id);
    try {

      // const { downloadUrl, fileName } = await response.json();
      const downloadUrl = download.download_url;
      const fileName = download.file_name;
      // Download the file using the secure URL
      const fileResponse = await fetch(downloadUrl);
      if (!fileResponse.ok) {
        throw new Error('Failed to download file');
      }

      const blob = await fileResponse.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Refresh downloads list to show updated count
      fetchDownloads();
    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Failed to download file. Please try again.');
    } finally {
      setDownloading(null);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (fileType.startsWith('video/')) return <Video className="w-5 h-5" />;
    if (fileType.startsWith('audio/')) return <Music className="w-5 h-5" />;
    if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('tar')) return <Archive className="w-5 h-5" />;
    if (fileType.includes('pdf') || fileType.includes('doc') || fileType.includes('txt')) return <FileText className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>My Downloads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">My Downloads</h1>
        <Badge variant="secondary">{downloads.length} files</Badge>
      </div>

      {downloads.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <File className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No downloads yet</h3>
            <p className="text-gray-500">
              Your purchased digital products will appear here once you make a purchase.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {downloads.map((download) => (
            <Card key={download.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getFileIcon(download.file_type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {download.file_name}
                      </h3>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>{formatFileSize(download.file_size)}</span>
                        <span>•</span>
                        <span>Downloaded {download.download_count} times</span>
                        {download.last_download && (
                          <>
                            <span>•</span>
                            <span>Last: {formatDate(download.last_download)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleDownload(download)}
                      disabled={downloading === download.id}
                      className="flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      {downloading === download.id ? 'Downloading...' : 'Download'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';

interface Message {
  content: string
  created_at: string
  id: string
  order_id: string | null
  read: boolean | null
  recipient_name: string
  recipient_id?: string | null
  sender_name: string
  sender_id?: string | null
  subject: string | null
}

interface MessagesListProps {
  messages: Message[];
  onRead?: (id: string) => void;
  onMarkAllRead?: () => void;
  onSelectMessage?: (message: Message) => void;
  selectedMessageId?: string | null;
  onDialogClose?: () => void;
}

export const MessagesList = ({ messages, onRead, onMarkAllRead, onSelectMessage, selectedMessageId, onDialogClose }: MessagesListProps) => {
  const { user, profile } = useAuth();
  const userId = profile?.user_id || user?.id;
  const [openConversation, setOpenConversation] = React.useState<string | null>(null);
  const [reply, setReply] = React.useState('');
  const [sending, setSending] = React.useState(false);
  // Group messages by conversation (other user id)
  const conversations = React.useMemo(() => {
    const map = new Map();
    messages.forEach((msg) => {
      const otherId = msg.sender_id === userId ? msg.recipient_id : msg.sender_id;
      if (!map.has(otherId)) map.set(otherId, []);
      map.get(otherId).push(msg);
    });
    return Array.from(map.entries()).map(([otherId, msgs]) => {
      const lastMsg = msgs[0];
      return {
        otherId,
        otherName: lastMsg.sender_id === userId ? lastMsg.recipient_name : lastMsg.sender_name,
        otherAvatar: '/placeholder.svg',
        lastSubject: lastMsg.subject,
        lastDate: lastMsg.created_at,
        messages: msgs.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()),
      };
    });
  }, [messages, userId]);

  if (openConversation) {
    const convo = conversations.find(c => c.otherId === openConversation);
    return (
      <div className="flex flex-col h-[500px] border rounded bg-white">
        <div className="flex items-center gap-2 border-b p-3">
          <Avatar><AvatarFallback>{convo.otherName[0]}</AvatarFallback></Avatar>
          <span className="font-semibold">{convo.otherName}</span>
        </div>
        <div className="flex-1 overflow-y-auto p-4 space-y-2 bg-gray-50">
          {convo.messages.map((msg) => {
            const isMe = msg.sender_id === userId;
            return (
              <div key={msg.id} className={`flex ${isMe ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs rounded-lg px-3 py-2 ${isMe ? 'bg-blue-500 text-white' : 'bg-white border'}`}> 
                  <div className="text-xs text-gray-300 mb-1">{new Date(msg.created_at).toLocaleString()}</div>
                  <div className="font-medium">{msg.content}</div>
                </div>
              </div>
            );
          })}
        </div>
        <form className="flex items-center gap-2 border-t p-3" onSubmit={async (e) => {
          e.preventDefault();
          if (!reply.trim()) return;
          setSending(true);
          const convoUser = convo.otherId;
          const { error, data } = await supabase.from('messages').insert({
            sender_id: userId,
            sender_name: profile?.full_name || user?.email,
            recipient_id: convoUser,
            recipient_name: convo.otherName,
            subject: '',
            content: reply,
            read: false,
            created_at: new Date().toISOString(),
          }).select().single();
          setSending(false);
          setReply('');
        }}>
          <Input
            value={reply}
            onChange={e => setReply(e.target.value)}
            placeholder="Type your message..."
            className="flex-1"
            disabled={sending}
          />
          <button type="submit" className="p-2 rounded-full bg-blue-500 text-white disabled:opacity-50" disabled={sending || !reply.trim()}>
            <Send className="w-5 h-5" />
          </button>
        </form>
        <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-700" onClick={() => setOpenConversation(null)}>×</button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {conversations.length === 0 && <div className="text-gray-500 text-center py-8">No conversations yet.</div>}
      {conversations.map(convo => (
        <div key={convo.otherId} className="flex items-center gap-3 p-3 border rounded cursor-pointer hover:bg-gray-50" onClick={() => setOpenConversation(convo.otherId)}>
          <Avatar><AvatarFallback>{convo.otherName[0]}</AvatarFallback></Avatar>
          <div className="flex-1">
            <div className="font-semibold">{convo.otherName}</div>
            <div className="text-gray-600 text-sm truncate">{convo.lastSubject}</div>
          </div>
          <div className="text-xs text-gray-400">{new Date(convo.lastDate).toLocaleDateString()}</div>
        </div>
      ))}
    </div>
  );
};

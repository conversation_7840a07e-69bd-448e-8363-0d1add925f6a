import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Download, CheckCircle, Clock } from "lucide-react";

interface Purchase {
  id: number;
  title: string;
  seller: string;
  price: string;
  date: string;
  status: string;
  downloadUrl: string | null;
}

interface PurchaseHistoryProps {
  purchases: Purchase[];
}

export const PurchaseHistory = ({ purchases }: PurchaseHistoryProps) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'in-progress':
        return <Badge className="bg-blue-500"><Clock className="w-3 h-3 mr-1" />In Progress</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="grid gap-4">
      {purchases.map((purchase) => (
        <Card key={purchase.id}>
          <CardContent className="flex items-center justify-between p-6">
            <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarFallback>{purchase.seller[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold">{purchase.title}</h3>
                <p className="text-sm text-gray-600">by {purchase.seller}</p>
                <p className="text-sm text-gray-500">Purchased on {purchase.date}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {getStatusBadge(purchase.status)}
              <span className="font-semibold text-lg">{purchase.price}</span>
              {purchase.downloadUrl && (
                <Button size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

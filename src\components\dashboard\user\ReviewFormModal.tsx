import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Star } from 'lucide-react';

export default function ReviewFormModal({ order, onClose, onReviewSubmitted }) {
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { toast } = useToast();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!rating || !comment.trim()) {
      setError('Rating and comment are required.');
      return;
    }
    if (comment.length > 500) {
      setError('Comment must be 500 characters or less.');
      return;
    }
    setLoading(true);
    setError('');
    
    // Check if review already exists for this listing and buyer
    const { data: existing } = await supabase
      .from('reviews')
      .select('id')
      .eq('listing_id', order.listing_id)
      .eq('buyer_name', order.buyer_name)
      .eq('buyer_id', order.buyer_id)
      .single();
    
    if (existing) {
      setError('You have already reviewed this product.');
      setLoading(false);
      return;
    }
    
    const { error: insertError } = await supabase.from('reviews').insert({
      listing_id: order.listing_id,
      buyer_name: order.buyer_name,
      buyer_id: order.buyer_id,
      rating,
      comment
    });
    
    setLoading(false);
    if (insertError) {
      setError(insertError.message);
      toast({ title: 'Review Error', description: insertError.message });
    } else {
      toast({ title: 'Review Submitted', description: 'Thank you for your feedback!' });
      setRating(5);
      setComment('');
      onClose();
      if (onReviewSubmitted) onReviewSubmitted();
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Leave a Review</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="rating">Rating</Label>
            <div className="flex items-center gap-2 mt-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  className="focus:outline-none"
                >
                  <Star
                    className={`w-6 h-6 ${
                      star <= rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              <span className="ml-2 text-sm text-gray-600">{rating}/5</span>
            </div>
          </div>
          
          <div>
            <Label htmlFor="comment">Comment</Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value.slice(0, 500))}
              placeholder="Share your experience with this product..."
              className="mt-2"
              rows={4}
              maxLength={500}
              required
            />
            <div className="text-xs text-gray-500 mt-1">
              {comment.length}/500 characters
            </div>
          </div>
          
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
          
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Submitting...' : 'Submit Review'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 
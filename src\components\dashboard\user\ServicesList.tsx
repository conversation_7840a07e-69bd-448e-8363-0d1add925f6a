import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { CheckCircle } from "lucide-react";

interface Service {
  id: number;
  title: string;
  seller: string;
  status: string;
  nextBilling: string;
  price: string;
}

interface ServicesListProps {
  services: Service[];
}

export const ServicesList = ({ services }: ServicesListProps) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="grid gap-4">
      {services.map((service) => (
        <Card key={service.id}>
          <CardContent className="flex items-center justify-between p-6">
            <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarFallback>{service.seller[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold">{service.title}</h3>
                <p className="text-sm text-gray-600">by {service.seller}</p>
                <p className="text-sm text-gray-500">Next billing: {service.nextBilling}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {getStatusBadge(service.status)}
              <span className="font-semibold">{service.price}</span>
              <Button variant="outline" size="sm">Manage</Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

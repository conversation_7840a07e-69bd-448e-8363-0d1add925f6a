import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Download, FileText, Image, Code, Archive, CheckCircle } from 'lucide-react';

interface DownloadInfoProps {
  product: any;
  className?: string;
}

export const DownloadInfo = ({ product, className = "" }: DownloadInfoProps) => {
  // Extract file formats from tech_specs or features
  const getFileFormats = () => {
    if (product.tech_specs?.formats) {
      return product.tech_specs.formats.split(', ').map((format: string) => format.trim());
    }
    
    // Fallback based on product type and title
    const title = product.title.toLowerCase();
    if (title.includes('icon') || title.includes('logo')) {
      return ['SVG', 'PNG', 'AI', 'EPS'];
    } else if (title.includes('template') || title.includes('website')) {
      return ['HTML', 'CSS', 'JS', 'PSD'];
    } else if (title.includes('photo') || title.includes('image')) {
      return ['JPG', 'PNG', 'TIFF'];
    } else {
      return ['ZIP', 'PDF'];
    }
  };

  const getFileIcon = (format: string) => {
    const f = format.toLowerCase();
    if (['jpg', 'png', 'gif', 'svg', 'tiff'].includes(f)) return Image;
    if (['html', 'css', 'js', 'ai', 'eps', 'psd'].includes(f)) return Code;
    if (['pdf', 'doc', 'txt'].includes(f)) return FileText;
    if (['zip', 'rar'].includes(f)) return Archive;
    return Download;
  };

  const fileFormats = getFileFormats();
  const fileSize = product.tech_specs?.file_size || 'Varies';
  const totalFiles = product.tech_specs?.total_files || fileFormats.length;

  const downloadFeatures = [
    'Instant download after purchase',
    'Lifetime access to files',
    'Free updates for 1 year',
    'Commercial license included',
    'No subscription required'
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Download Details</h3>
        <p className="text-gray-600 text-sm">
          Get instant access to all files immediately after purchase.
        </p>
      </div>

      {/* File Formats */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">File Formats Included</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {fileFormats.map((format, index) => {
            const IconComponent = getFileIcon(format);
            return (
              <div
                key={index}
                className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border"
              >
                <IconComponent className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-900">{format}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Download Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-900">{totalFiles}</div>
          <div className="text-sm text-blue-700">Total Files</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-900">{fileSize}</div>
          <div className="text-sm text-green-700">Download Size</div>
        </div>
      </div>

      {/* What You Get */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">What You Get</h4>
        <div className="space-y-2">
          {downloadFeatures.map((feature, index) => (
            <div key={index} className="flex items-start gap-2">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* License Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
          <FileText className="w-4 h-4" />
          License Information
        </h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">License Type:</span>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              {product.license_type || 'Commercial'}
            </Badge>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Commercial Use:</span>
            <span className="text-green-600 font-medium">✓ Allowed</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Resale Rights:</span>
            <span className="text-red-600 font-medium">✗ Not Allowed</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Attribution:</span>
            <span className="text-gray-600">Not Required</span>
          </div>
        </div>
      </div>

      {/* Download Process */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">How It Works</h4>
        <div className="space-y-2 text-sm text-blue-800">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
            <span>Complete your purchase securely</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
            <span>Receive instant download link via email</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
            <span>Access your files anytime from your account</span>
          </div>
        </div>
      </div>
    </div>
  );
};

import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface RelatedProduct {
  title: string;
  price: string;
  rating: number;
  sales: number;
}

interface RelatedProductsProps {
  products: RelatedProduct[];
  title?: string;
}

export const RelatedProducts = ({ products, title = "Related Products" }: RelatedProductsProps) => {
  return (
    <div className="mt-16">
      <h2 className="text-2xl font-bold mb-8">{title}</h2>
      <div className="grid md:grid-cols-3 gap-6">
        {products.map((product, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
              <CardTitle className="text-lg">{product.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-xl font-bold text-primary">{product.price}</span>
                <div className="text-sm text-gray-500">
                  <span>{product.rating} ★ ({product.sales})</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
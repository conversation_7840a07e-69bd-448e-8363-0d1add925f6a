import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Clock, CheckCircle } from 'lucide-react';

interface Package {
  name: string;
  price: string;
  delivery: string;
  revisions: string;
}

interface PackageSelectorProps {
  packages: Package[];
  onPackageSelect?: (packageIndex: number) => void;
}

export const PackageSelector = ({ packages, onPackageSelect }: PackageSelectorProps) => {
  const [selectedPackage, setSelectedPackage] = useState(0);

  const handlePackageSelect = (index: number) => {
    setSelectedPackage(index);
    onPackageSelect?.(index);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Choose a Package</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 mb-4">
          {packages.map((pkg, index) => (
            <button
              key={index}
              onClick={() => handlePackageSelect(index)}
              className={`w-full p-3 rounded-lg border text-left transition-colors ${
                selectedPackage === index 
                  ? 'border-primary bg-primary/5' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <span className="font-semibold">{pkg.name}</span>
                <span className="font-bold text-primary">{pkg.price}</span>
              </div>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  {pkg.delivery} delivery
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  {pkg.revisions} revisions
                </div>
              </div>
            </button>
          ))}
        </div>

        <Button size="lg" className="w-full">
          Continue ({packages[selectedPackage].price})
        </Button>
        
        <p className="text-xs text-gray-500 text-center mt-2">
          You won't be charged yet
        </p>
      </CardContent>
    </Card>
  );
};
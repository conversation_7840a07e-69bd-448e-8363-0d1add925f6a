import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FileText, Upload, Calendar } from 'lucide-react';

interface RequirementsFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (requirements: ServiceRequirements) => void;
  service: any;
  className?: string;
}

interface ServiceRequirements {
  projectTitle: string;
  description: string;
  timeline: string;
  budget: string;
  additionalNotes: string;
  attachments?: File[];
}

export const RequirementsForm = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  service,
  className = "" 
}: RequirementsFormProps) => {
  const [requirements, setRequirements] = useState<ServiceRequirements>({
    projectTitle: '',
    description: '',
    timeline: '',
    budget: service?.price?.toString() || '',
    additionalNotes: '',
    attachments: []
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(requirements);
  };

  const handleInputChange = (field: keyof ServiceRequirements, value: string) => {
    setRequirements(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Project Requirements - {service?.title}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Project Title */}
          <div>
            <Label htmlFor="projectTitle">Project Title *</Label>
            <Input
              id="projectTitle"
              value={requirements.projectTitle}
              onChange={(e) => handleInputChange('projectTitle', e.target.value)}
              placeholder="Give your project a clear title"
              required
            />
          </div>

          {/* Project Description */}
          <div>
            <Label htmlFor="description">Project Description *</Label>
            <Textarea
              id="description"
              value={requirements.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your project in detail. What do you need? What are your goals? Any specific requirements?"
              rows={4}
              required
            />
          </div>

          {/* Timeline and Budget */}
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="timeline" className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Preferred Timeline
              </Label>
              <Input
                id="timeline"
                value={requirements.timeline}
                onChange={(e) => handleInputChange('timeline', e.target.value)}
                placeholder="e.g., 2 weeks, ASAP, flexible"
              />
            </div>
            <div>
              <Label htmlFor="budget">Budget</Label>
              <Input
                id="budget"
                value={requirements.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                placeholder="$0.00"
                type="number"
                min="0"
                step="0.01"
              />
            </div>
          </div>

          {/* Additional Notes */}
          <div>
            <Label htmlFor="additionalNotes">Additional Notes</Label>
            <Textarea
              id="additionalNotes"
              value={requirements.additionalNotes}
              onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
              placeholder="Any additional information, preferences, or special requests"
              rows={3}
            />
          </div>

          {/* File Upload */}
          <div>
            <Label className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Attachments (Optional)
            </Label>
            <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">
                Upload reference files, mockups, or any relevant documents
              </p>
              <Button type="button" variant="outline" size="sm">
                Choose Files
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                Max 10MB per file. Supported: PDF, DOC, JPG, PNG
              </p>
            </div>
          </div>

          {/* Service Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">Service Summary</h4>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Service:</span>
                <div className="font-medium">{service?.title}</div>
              </div>
              <div>
                <span className="text-gray-600">Price:</span>
                <div className="font-medium">${service?.price}</div>
              </div>
              {service?.delivery_time && (
                <div>
                  <span className="text-gray-600">Delivery:</span>
                  <div className="font-medium">{service.delivery_time} days</div>
                </div>
              )}
              {service?.revisions && (
                <div>
                  <span className="text-gray-600">Revisions:</span>
                  <div className="font-medium">
                    {service.revisions === 999 ? 'Unlimited' : service.revisions}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-600">
              The seller will review your requirements and respond within 24 hours.
            </div>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                Submit Requirements
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

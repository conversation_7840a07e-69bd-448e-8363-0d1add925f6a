import React from 'react';
import { Button } from '@/components/ui/button';
import { Clock, RotateCcw, CheckCircle } from 'lucide-react';

interface ServicePackage {
  id: string;
  name: string;
  description: string;
  price: number;
  deliveryTime: number;
  revisions: number;
  features: string[];
}

interface ServicePackagesProps {
  packages?: ServicePackage[];
  defaultPrice?: number;
  defaultDeliveryTime?: number;
  defaultRevisions?: number;
  features?: string[];
  onSelectPackage: (packageId: string, price: number) => void;
  className?: string;
}

export const ServicePackages = ({ 
  packages, 
  defaultPrice,
  defaultDeliveryTime,
  defaultRevisions,
  features = [],
  onSelectPackage,
  className = "" 
}: ServicePackagesProps) => {
  // If no packages are defined, create a default package
  const servicePackages = packages || [{
    id: 'standard',
    name: 'Standard Package',
    description: 'Complete service delivery with professional quality',
    price: defaultPrice || 0,
    deliveryTime: defaultDeliveryTime || 7,
    revisions: defaultRevisions || 2,
    features: features.length > 0 ? features : [
      'Professional service delivery',
      'High-quality work',
      'Timely completion',
      'Customer support'
    ]
  }];

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">Service Packages</h3>
      
      {servicePackages.map((pkg) => (
        <div key={pkg.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
          {/* Package Header */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <h4 className="text-lg font-semibold text-gray-900">{pkg.name}</h4>
              <p className="text-gray-600 text-sm mt-1">{pkg.description}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">${pkg.price}</div>
            </div>
          </div>

          {/* Package Details */}
          <div className="grid md:grid-cols-2 gap-4 mb-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>{pkg.deliveryTime} days delivery</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <RotateCcw className="w-4 h-4" />
              <span>
                {pkg.revisions === 999 ? 'Unlimited' : pkg.revisions} revisions
              </span>
            </div>
          </div>

          {/* Package Features */}
          <div className="mb-6">
            <h5 className="font-medium text-gray-900 mb-2">What's included:</h5>
            <div className="grid md:grid-cols-2 gap-2">
              {pkg.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Select Button */}
          <Button 
            onClick={() => onSelectPackage(pkg.id, pkg.price)}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            Select {pkg.name} - ${pkg.price}
          </Button>
        </div>
      ))}

      {/* Custom Package Option */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <h4 className="font-semibold text-gray-900 mb-2">Need something custom?</h4>
        <p className="text-gray-600 text-sm mb-4">
          Contact the seller for a personalized quote tailored to your specific needs.
        </p>
        <Button variant="outline">
          Request Custom Quote
        </Button>
      </div>
    </div>
  );
};

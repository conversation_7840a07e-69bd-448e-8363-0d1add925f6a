import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Heart, Share2, ShoppingCart, Eye } from 'lucide-react';

interface ActionButtonsProps {
  primaryAction: {
    label: string;
    icon?: React.ReactNode;
    loading?: boolean;
  };
  showPreview?: boolean;
  showWishlist?: boolean;
  showShare?: boolean;
  isInWishlist?: boolean;
  onPrimaryAction?: () => void;
  onPreview?: () => void;
  onWishlist?: () => void;
  onShare?: () => void;
}

export const ActionButtons = ({
  primaryAction,
  showPreview = true,
  showWishlist = true,
  showShare = true,
  isInWishlist = false,
  onPrimaryAction,
  onPreview,
  onWishlist,
  onShare
}: ActionButtonsProps) => {
  return (
    <div className="space-y-3">
      <Button size="lg" className="w-full" onClick={onPrimaryAction} disabled={primaryAction.loading}>
        {primaryAction.loading ? (
          <span className="flex items-center justify-center w-full">
            <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
            Loading...
          </span>
        ) : (
          <>
            {primaryAction.icon || <ShoppingCart className="w-5 h-5 mr-2" />}
            {primaryAction.label}
          </>
        )}
      </Button>
      
      <div className="flex gap-3">
        {showWishlist && (
          <Button 
            variant="outline" 
            size="lg" 
            className="flex-1" 
            onClick={onWishlist}
          >
            <Heart className={`w-5 h-5 mr-2 ${isInWishlist ? 'text-red-500 fill-current' : ''}`} />
            {'Wishlist'}
          </Button>
        )}
        {showPreview && (
          <Button variant="outline" size="lg" className="flex-1" onClick={onPreview}>
            <Eye className="w-5 h-5 mr-2" />
            Preview
          </Button>
        )}
        {showShare && (
          <Button variant="outline" size="lg" onClick={onShare}>
            <Share2 className="w-5 h-5" />
          </Button>
        )}
      </div>
    </div>
  );
};
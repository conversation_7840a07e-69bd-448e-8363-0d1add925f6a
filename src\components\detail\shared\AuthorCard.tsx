import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, MessageCircle, Heart, Share2 } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface AuthorCardProps {
  author: {
    name: string;
    title?: string;
    avatar: string;
    rating: number;
    sales?: number;
    completedJobs?: number;
    responseTime?: string;
    languages?: string[];
    memberSince?: string;
    id?: string; // seller user_id
  };
  showContactButton?: boolean;
  showFollowButton?: boolean;
  showActions?: boolean;
  onMessageSent?: (msg: any) => void;
}

export const AuthorCard = ({ 
  author, 
  showContactButton = false, 
  showFollowButton = true,
  showActions = false,
  onMessageSent
}: AuthorCardProps) => {
  const [open, setOpen] = React.useState(false);
  const [subject, setSubject] = React.useState("");
  const [message, setMessage] = React.useState("");
  const [sending, setSending] = React.useState(false);
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();
    setSending(true);
    if (!user || !profile) {
      toast({ title: 'Login required', description: 'Please log in to send a message.', variant: 'destructive' });
      setSending(false);
      return;
    }
    const { error, data } = await supabase.from('messages').insert({
      sender_name: profile.full_name || user.email,
      sender_id: profile.user_id,
      recipient_name: author.name,
      recipient_id: author.id,
      subject,
      content: message,
      read: false,
      created_at: new Date().toISOString(),
    }).select().single();
    setSending(false);
    if (error) {
      toast({ title: 'Error', description: 'Failed to send message.' });
      return;
    }
    toast({ title: 'Message sent', description: 'Your message has been sent to the seller.' });
    setOpen(false);
    setSubject("");
    setMessage("");
    if (onMessageSent) onMessageSent(data);
  };
  return (
    <Card>
      <CardContent className={showContactButton ? "pt-6" : "flex items-center gap-4 pt-4"}>
        <div className={`flex items-center gap-4 ${showContactButton ? 'mb-4' : 'flex-1'}`}>
          <img 
            src={author.avatar} 
            alt={author.name} 
            className={showContactButton ? "w-16 h-16 rounded-full" : "w-12 h-12 rounded-full"} 
          />
          <div className="flex-1">
            <h3 className={`font-bold ${showContactButton ? 'text-lg' : 'font-semibold'}`}>{author.name}</h3>
            {author.title && <p className="text-gray-600">{author.title}</p>}
            <div className="flex items-center gap-1 mt-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span className="text-sm">{author.rating}</span>
            </div>
          </div>
          {showFollowButton && !showContactButton && (
            <Button variant="outline" size="sm">Follow</Button>
          )}
        </div>
        
        {showContactButton && (
          <>
            <div className="space-y-2 text-sm text-gray-600 mb-4">
              {author.responseTime && (
                <div className="flex justify-between">
                  <span>Response time:</span>
                  <span>{author.responseTime}</span>
                </div>
              )}
              {author.completedJobs && (
                <div className="flex justify-between">
                  <span>Completed jobs:</span>
                  <span>{author.completedJobs}</span>
                </div>
              )}
              {author.sales && (
                <div className="flex justify-between">
                  <span>Sales:</span>
                  <span>{author.sales}</span>
                </div>
              )}
              {author.memberSince && (
                <div className="flex justify-between">
                  <span>Member since:</span>
                  <span>{author.memberSince}</span>
                </div>
              )}
            </div>

            {author.languages && (
              <div className="flex gap-2 mb-4">
                {author.languages.map((lang, index) => (
                  <Badge key={index} variant="outline" className="text-xs">{lang}</Badge>
                ))}
              </div>
            )}

            <Button variant="outline" className="w-full mb-2" onClick={() => setOpen(true)}>
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Seller
            </Button>
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Contact Seller</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSend} className="space-y-4">
                  <Input
                    placeholder="Subject"
                    value={subject}
                    onChange={e => setSubject(e.target.value)}
                    required
                  />
                  <Textarea
                    placeholder="Type your message..."
                    value={message}
                    onChange={e => setMessage(e.target.value)}
                    required
                  />
                  <Button type="submit" className="w-full" disabled={sending}>
                    {sending ? "Sending..." : "Send Message"}
                  </Button>
                </form>
              </DialogContent>
            </Dialog>
            
            {showActions && (
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Heart className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { RatingDisplay } from './RatingDisplay';
import { PriceDisplay } from './PriceDisplay';
import { TagsList } from './TagsList';

interface DetailHeroProps {
  category: string;
  title: string;
  tagline?: string;
  rating: number;
  reviews: number;
  sales?: number;
  completedOrders?: number;
  price: string;
  originalPrice?: string;
  discountPercent?: string;
  startingPrice?: string;
  tags: string[];
}

export const DetailHero = ({
  category,
  title,
  tagline,
  rating,
  reviews,
  sales,
  completedOrders,
  price,
  originalPrice,
  discountPercent,
  startingPrice,
  tags
}: DetailHeroProps) => {
  return (
    <div>
      <Badge variant="secondary" className="mb-3">{category}</Badge>
      <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">{title}</h1>
      {tagline && <p className="text-xl text-gray-600 mb-6">{tagline}</p>}
      
      <RatingDisplay 
        rating={rating} 
        reviews={reviews} 
        sales={sales}
        completedOrders={completedOrders}
      />

      <PriceDisplay 
        price={price}
        originalPrice={originalPrice}
        discountPercent={discountPercent}
        startingPrice={startingPrice}
      />

      <TagsList tags={tags} />
    </div>
  );
};
import React from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";

interface TabItem {
  value: string;
  label: string;
  content: React.ReactNode;
}

interface DetailTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
}

export const DetailTabs = ({ tabs, defaultValue }: DetailTabsProps) => {
  return (
    <Tabs defaultValue={defaultValue || tabs[0]?.value} className="w-full">
      <TabsList className={`grid w-full grid-cols-${tabs.length}`}>
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value} className="mt-6">
          {typeof tab.content === 'string' ? (
            <Card>
              <CardContent className="pt-6">
                <p className="text-gray-700 leading-relaxed">{tab.content}</p>
              </CardContent>
            </Card>
          ) : (
            tab.content
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
};
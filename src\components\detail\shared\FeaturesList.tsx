import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Download } from 'lucide-react';

interface FeaturesListProps {
  features: string[] | Array<{ title: string; description: string }>;
  type?: 'simple' | 'detailed' | 'included';
}

export const FeaturesList = ({ features, type = 'simple' }: FeaturesListProps) => {
  const getIcon = () => {
    switch (type) {
      case 'included':
        return Download;
      case 'detailed':
        return null;
      default:
        return CheckCircle;
    }
  };

  const Icon = getIcon();
  const iconColor = type === 'included' ? 'text-primary' : 'text-green-500';

  if (type === 'detailed' && Array.isArray(features) && typeof features[0] === 'object') {
    return (
      <div className="space-y-4">
        {(features as Array<{ title: string; description: string }>).map((feature, index) => (
          <Card key={index}>
            <CardContent className="pt-6">
              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-gray-700">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <ul className="space-y-3">
          {(features as string[]).map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              {Icon && <Icon className={`w-5 h-5 ${iconColor} mt-0.5 flex-shrink-0`} />}
              {!Icon && <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>}
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};
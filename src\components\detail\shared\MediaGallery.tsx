import React, { useState } from 'react';
import { ImageIcon, ZoomIn, ExternalLink } from 'lucide-react';

interface MediaGalleryProps {
  images: string[];
  title: string;
  mainImage?: string;
  productType?: 'product' | 'service' | 'tool';
  className?: string;
}

export const MediaGallery = ({
  images,
  title,
  mainImage,
  productType = 'product',
  className = ""
}: MediaGalleryProps) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);

  // Combine main image with gallery images, prioritizing gallery images
  const allImages = images && images.length > 0 ? images : (mainImage ? [mainImage] : []);

  const getTypeSpecificPlaceholder = () => {
    switch (productType) {
      case 'service':
        return {
          icon: <ImageIcon className="w-16 h-16 mx-auto mb-2" />,
          text: 'Service portfolio coming soon',
          bgColor: 'bg-green-50'
        };
      case 'tool':
        return {
          icon: <ExternalLink className="w-16 h-16 mx-auto mb-2" />,
          text: 'Tool screenshots coming soon',
          bgColor: 'bg-purple-50'
        };
      default:
        return {
          icon: <ImageIcon className="w-16 h-16 mx-auto mb-2" />,
          text: 'Product images coming soon',
          bgColor: 'bg-blue-50'
        };
    }
  };

  const placeholder = getTypeSpecificPlaceholder();

  if (allImages.length === 0) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className={`aspect-video ${placeholder.bgColor} rounded-lg overflow-hidden flex items-center justify-center border-2 border-dashed border-gray-300`}>
          <div className="text-center text-gray-400">
            {placeholder.icon}
            <p className="text-sm">{placeholder.text}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Image */}
      <div className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden shadow-lg group">
        <img
          src={allImages[selectedImage]}
          alt={`${title} preview`}
          className={`w-full h-full object-cover transition-transform duration-300 ${
            isZoomed ? 'scale-150 cursor-zoom-out' : 'hover:scale-105 cursor-zoom-in'
          }`}
          onClick={() => setIsZoomed(!isZoomed)}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTI1SDE4NVYxMzVIMTc1VjEyNVoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE2NSAxNDVIMjM1VjE1NUgxNjVWMTQ1WiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTg1IDE2NUgxOTVWMTc1SDE4NVYxNjVaIiBmaWxsPSIjOUNBM0FGIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTkwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pgo8L3N2Zz4K';
          }}
        />

        {/* Zoom indicator */}
        <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
          <ZoomIn className="w-4 h-4" />
        </div>

        {/* Image counter */}
        {allImages.length > 1 && (
          <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
            {selectedImage + 1} / {allImages.length}
          </div>
        )}
      </div>

      {/* Thumbnail Grid */}
      {allImages.length > 1 && (
        <div className="grid grid-cols-4 gap-2">
          {allImages.map((image, index) => (
            <button
              key={index}
              onClick={() => {
                setSelectedImage(index);
                setIsZoomed(false);
              }}
              className={`aspect-video bg-gray-200 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                selectedImage === index
                  ? 'border-blue-500 ring-2 ring-blue-200 shadow-md'
                  : 'border-transparent hover:border-gray-300 hover:shadow-md'
              }`}
            >
              <img
                src={image}
                alt={`Preview ${index + 1}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA0MCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMTUiIHI9IjUiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cg==';
                }}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
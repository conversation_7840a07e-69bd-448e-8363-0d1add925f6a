import React from 'react';
import { Badge } from "@/components/ui/badge";

interface PriceDisplayProps {
  price: string;
  originalPrice?: string;
  discountPercent?: string;
  startingPrice?: string;
}

export const PriceDisplay = ({ price, originalPrice, discountPercent, startingPrice }: PriceDisplayProps) => {
  if (startingPrice) {
    return (
      <div className="mb-6">
        <span className="text-3xl font-bold text-primary">Starting at {startingPrice}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 mb-6">
      <span className="text-3xl font-bold text-primary">{price}</span>
      {originalPrice && (
        <>
          <span className="text-xl text-gray-500 line-through">{originalPrice}</span>
          {discountPercent && <Badge variant="destructive">{discountPercent} OFF</Badge>}
        </>
      )}
    </div>
  );
};
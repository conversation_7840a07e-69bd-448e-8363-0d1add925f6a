import React from 'react';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Clock, RotateCcw } from 'lucide-react';

interface PricingSectionProps {
  product: any;
  onPurchase: () => void;
  loading?: boolean;
  isOwner?: boolean;
  hasPurchased?: boolean;
  className?: string;
}

export const PricingSection = ({ 
  product, 
  onPurchase, 
  loading = false, 
  isOwner = false, 
  hasPurchased = false,
  className = "" 
}: PricingSectionProps) => {
  const getTypeSpecificInfo = () => {
    switch (product.type) {
      case 'service':
        return {
          deliveryLabel: 'Delivery Time',
          deliveryValue: product.delivery_time ? `${product.delivery_time} days` : 'Custom',
          additionalInfo: product.revisions ? `${product.revisions} revisions included` : 'Unlimited revisions',
          buttonText: 'Order Service',
          buttonIcon: <Clock className="w-5 h-5 mr-2" />
        };
      case 'tool':
        return {
          deliveryLabel: 'Access',
          deliveryValue: 'Instant',
          additionalInfo: product.license_type || 'Commercial License',
          buttonText: 'Get Tool',
          buttonIcon: <ShoppingCart className="w-5 h-5 mr-2" />
        };
      case 'product':
      default:
        return {
          deliveryLabel: 'Delivery',
          deliveryValue: 'Instant Download',
          additionalInfo: product.license_type || 'Commercial License',
          buttonText: 'Buy Now',
          buttonIcon: <ShoppingCart className="w-5 h-5 mr-2" />
        };
    }
  };

  const typeInfo = getTypeSpecificInfo();

  if (isOwner) {
    return (
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center gap-2 mb-3">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span className="font-semibold text-blue-800">Your {product.type === 'service' ? 'Service' : product.type === 'tool' ? 'Tool' : 'Product'}</span>
        </div>
        <p className="text-blue-700 text-sm mb-4">
          This is your {product.type}. Manage it from your dashboard.
        </p>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            Preview
          </Button>
          <Button variant="outline" size="sm">
            Manage
          </Button>
        </div>
      </div>
    );
  }

  if (hasPurchased) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center gap-2 mb-3">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="font-semibold text-green-800">Already Purchased</span>
        </div>
        <p className="text-green-700 text-sm mb-4">
          You own this {product.type}. Access your {product.type === 'product' ? 'downloads' : 'orders'} from your dashboard.
        </p>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            Preview
          </Button>
          <Button variant="outline" size="sm">
            My Orders
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`}>
      {/* Price */}
      <div className="mb-6">
        <div className="text-3xl font-bold text-gray-900 mb-2">
          ${product.price}
        </div>
        <div className="text-sm text-gray-600">
          One-time purchase • {typeInfo.deliveryValue}
        </div>
      </div>

      {/* Product Info */}
      <div className="space-y-3 mb-6 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">{typeInfo.deliveryLabel}:</span>
          <span className="text-gray-900 font-medium">{typeInfo.deliveryValue}</span>
        </div>
        {product.revisions && (
          <div className="flex justify-between">
            <span className="text-gray-600">Revisions:</span>
            <span className="text-gray-900 font-medium">
              {product.revisions === 999 ? 'Unlimited' : product.revisions}
            </span>
          </div>
        )}
        <div className="flex justify-between">
          <span className="text-gray-600">License:</span>
          <span className="text-gray-900 font-medium">{typeInfo.additionalInfo}</span>
        </div>
      </div>

      {/* Purchase Button */}
      <Button 
        onClick={onPurchase}
        disabled={loading}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-semibold"
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Processing...
          </div>
        ) : (
          <>
            {typeInfo.buttonIcon}
            {typeInfo.buttonText} - ${product.price}
          </>
        )}
      </Button>

      {/* Secondary Actions */}
      <div className="flex gap-2 mt-3">
        <Button variant="outline" className="flex-1">
          Add to Wishlist
        </Button>
        <Button variant="outline" className="flex-1">
          Preview
        </Button>
        <Button variant="outline">
          Share
        </Button>
      </div>
    </div>
  );
};

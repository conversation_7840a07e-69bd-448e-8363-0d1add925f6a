import React from 'react';

interface ProductHeaderProps {
  product: any;
  avgRating: number;
  reviewCount: number;
  className?: string;
}

export const ProductHeader = ({ product, avgRating, reviewCount, className = "" }: ProductHeaderProps) => {
  const getTypeSpecificBadge = () => {
    switch (product.type) {
      case 'service':
        return {
          label: 'Professional Service',
          color: 'bg-green-50 text-green-600 border-green-200'
        };
      case 'tool':
        return {
          label: 'Micro SaaS Tool',
          color: 'bg-purple-50 text-purple-600 border-purple-200'
        };
      case 'product':
      default:
        return {
          label: 'Digital Product',
          color: 'bg-blue-50 text-blue-600 border-blue-200'
        };
    }
  };

  const badge = getTypeSpecificBadge();

  return (
    <div className={className}>
      {/* Category Badge */}
      <div className="flex items-center gap-2 mb-3">
        <span className={`text-sm font-medium px-3 py-1 rounded-full border ${badge.color}`}>
          {badge.label}
        </span>
        {product.featured && (
          <span className="text-sm font-medium px-3 py-1 rounded-full bg-yellow-50 text-yellow-600 border border-yellow-200">
            Featured
          </span>
        )}
      </div>

      {/* Title */}
      <h1 className="text-3xl font-bold text-gray-900 mb-4 leading-tight">
        {product.title}
      </h1>
      
      {/* Rating and Reviews */}
      <div className="flex items-center gap-4 mb-6">
        <div className="flex items-center gap-1">
          {[...Array(5)].map((_, i) => (
            <svg
              key={i}
              className={`w-5 h-5 ${
                i < Math.floor(avgRating) ? 'text-yellow-400' : 'text-gray-300'
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
          <span className="text-sm text-gray-600 ml-2">
            {avgRating > 0 ? avgRating.toFixed(1) : '0.0'} ({reviewCount} reviews)
          </span>
        </div>
      </div>

      {/* Tags */}
      {product.tags && product.tags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {product.tags.map((tag: string, index: number) => (
            <span
              key={index}
              className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 transition-colors"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

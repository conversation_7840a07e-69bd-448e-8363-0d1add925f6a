import React from 'react';

interface ProductInfoProps {
  product: any;
  className?: string;
}

export const ProductInfo = ({ product, className = "" }: ProductInfoProps) => {
  const getTypeSpecificInfo = () => {
    const baseInfo = [
      {
        label: 'Type',
        value: product.type === 'product' ? 'Digital Product' : 
               product.type === 'service' ? 'Service' : 'Tool'
      },
      {
        label: 'Last updated',
        value: new Date(product.updated_at).toLocaleDateString()
      }
    ];

    switch (product.type) {
      case 'service':
        return [
          ...baseInfo,
          ...(product.delivery_time ? [{
            label: 'Delivery Time',
            value: `${product.delivery_time} days`
          }] : []),
          ...(product.revisions ? [{
            label: 'Revisions',
            value: product.revisions === 999 ? 'Unlimited' : `${product.revisions} rounds`
          }] : [])
        ];
      
      case 'tool':
        return [
          ...baseInfo,
          {
            label: 'Access',
            value: 'Instant'
          },
          ...(product.license_type ? [{
            label: 'License',
            value: product.license_type
          }] : [])
        ];
      
      case 'product':
      default:
        return [
          ...baseInfo,
          {
            label: 'Delivery',
            value: 'Instant download'
          },
          ...(product.license_type ? [{
            label: 'License',
            value: product.license_type
          }] : [])
        ];
    }
  };

  const infoItems = getTypeSpecificInfo();

  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`}>
      <h3 className="font-semibold text-gray-900 mb-4">
        {product.type === 'service' ? 'Service' : 
         product.type === 'tool' ? 'Tool' : 'Product'} Information
      </h3>
      
      <div className="space-y-3 text-sm">
        {infoItems.map((item, index) => (
          <div key={index} className="flex justify-between">
            <span className="text-gray-600">{item.label}:</span>
            <span className="text-gray-900 font-medium">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

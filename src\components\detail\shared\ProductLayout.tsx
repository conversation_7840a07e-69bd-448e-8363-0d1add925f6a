import React from 'react';
import { Link } from 'react-router-dom';
import { PageLayout } from "@/components/layout/PageLayout";

interface ProductLayoutProps {
  children: React.ReactNode;
  product: any;
  className?: string;
}

export const ProductLayout = ({ children, product, className = "" }: ProductLayoutProps) => {
  // Dynamic breadcrumb based on product type
  const getBreadcrumbPath = () => {
    switch (product.type) {
      case 'product':
        return { path: '/digital-products', label: 'Digital Products' };
      case 'service':
        return { path: '/services', label: 'Services' };
      case 'tool':
        return { path: '/micro-saas', label: 'Micro SaaS' };
      default:
        return { path: '/digital-products', label: 'Digital Products' };
    }
  };

  const breadcrumb = getBreadcrumbPath();

  return (
    <PageLayout>
      <div className={`max-w-7xl mx-auto px-4 py-8 ${className}`}>
        {/* Dynamic Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link to="/" className="hover:text-gray-900 transition-colors">
            Home
          </Link>
          <span>/</span>
          <Link to={breadcrumb.path} className="hover:text-gray-900 transition-colors">
            {breadcrumb.label}
          </Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">{product.title}</span>
        </nav>

        {children}
      </div>
    </PageLayout>
  );
};

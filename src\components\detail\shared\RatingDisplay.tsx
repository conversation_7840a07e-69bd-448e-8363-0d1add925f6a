import React from 'react';
import { Star } from 'lucide-react';

interface RatingDisplayProps {
  rating: number;
  reviews: number;
  sales?: number;
  completedOrders?: number;
}

export const RatingDisplay = ({ rating, reviews, sales, completedOrders }: RatingDisplayProps) => {
  return (
    <div className="flex items-center gap-4 mb-4 lg:mb-6">
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={`w-5 h-5 ${i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`} 
          />
        ))}
        <span className="ml-2 text-gray-600">{rating} ({reviews} reviews)</span>
      </div>
      {(sales || completedOrders) && (
        <>
          <span className="text-gray-500">•</span>
          <span className="text-gray-600">
            {sales ? `${sales} sales` : `${completedOrders} orders completed`}
          </span>
        </>
      )}
    </div>
  );
};
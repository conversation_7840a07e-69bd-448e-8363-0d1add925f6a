import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Star } from 'lucide-react';

interface Review {
  name: string;
  role?: string;
  avatar: string;
  content: string;
  rating?: number;
  date?: string;
}

interface ReviewsSectionProps {
  reviews: Review[];
  title?: string;
}

export const ReviewsSection = ({ reviews, title = "Reviews" }: ReviewsSectionProps) => {
  return (
    <div className="space-y-6">
      {reviews.map((review, index) => (
        <Card key={index}>
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <img src={review.avatar} alt={review.name} className="w-12 h-12 rounded-full" />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold">{review.name}</h4>
                  {review.role && (
                    <>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-500">{review.role}</span>
                    </>
                  )}
                </div>
                {review.rating && (
                  <div className="flex items-center mb-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                    ))}
                  </div>
                )}
                <p className="text-gray-700 mb-2">{review.content}</p>
                {review.date && <p className="text-sm text-gray-500">{review.date}</p>}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
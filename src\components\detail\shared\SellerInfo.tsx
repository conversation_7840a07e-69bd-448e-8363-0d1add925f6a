import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle, UserPlus, Star } from 'lucide-react';

interface SellerInfoProps {
  seller: {
    id?: string;
    name: string;
    avatar?: string;
    rating: number;
    sales: number;
    responseTime?: string;
    memberSince?: string;
  };
  onContact?: () => void;
  onFollow?: () => void;
  isFollowing?: boolean;
  className?: string;
}

export const SellerInfo = ({ 
  seller, 
  onContact, 
  onFollow, 
  isFollowing = false,
  className = "" 
}: SellerInfoProps) => {
  return (
    <div className={`bg-gray-50 border rounded-lg p-6 ${className}`}>
      <h3 className="font-semibold text-gray-900 mb-4">About the Seller</h3>
      
      {/* Seller Profile */}
      <div className="flex items-center gap-4 mb-4">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
          {seller.name[0].toUpperCase()}
        </div>
        <div className="flex-1">
          <h4 className="font-semibold text-gray-900">{seller.name}</h4>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span>{seller.rating.toFixed(1)}</span>
            </div>
            <span>•</span>
            <span>{seller.sales} sales</span>
          </div>
        </div>
      </div>

      {/* Seller Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        {seller.responseTime && (
          <div>
            <span className="text-gray-600">Response time:</span>
            <div className="font-medium text-gray-900">{seller.responseTime}</div>
          </div>
        )}
        {seller.memberSince && (
          <div>
            <span className="text-gray-600">Member since:</span>
            <div className="font-medium text-gray-900">{seller.memberSince}</div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        {onContact && (
          <Button 
            variant="outline" 
            className="flex-1"
            onClick={onContact}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Contact
          </Button>
        )}
        {onFollow && (
          <Button 
            variant="outline"
            onClick={onFollow}
            className={isFollowing ? "bg-blue-50 border-blue-200 text-blue-700" : ""}
          >
            <UserPlus className="w-4 h-4 mr-2" />
            {isFollowing ? 'Following' : 'Follow'}
          </Button>
        )}
      </div>
    </div>
  );
};

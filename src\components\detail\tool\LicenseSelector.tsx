import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle } from 'lucide-react';

interface License {
  name: string;
  price: string;
  features: string[];
  recommended?: boolean;
}

interface LicenseSelectorProps {
  licenses: License[];
  onLicenseSelect?: (licenseIndex: number) => void;
}

export const LicenseSelector = ({ licenses, onLicenseSelect }: LicenseSelectorProps) => {
  const [selectedLicense, setSelectedLicense] = useState(0);

  const handleLicenseSelect = (index: number) => {
    setSelectedLicense(index);
    onLicenseSelect?.(index);
  };

  return (
    <Card className="sticky top-8">
      <CardHeader>
        <CardTitle>Choose License</CardTitle>
        <CardDescription>Select the license that fits your needs</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 mb-6">
          {licenses.map((license, index) => (
            <button
              key={index}
              onClick={() => handleLicenseSelect(index)}
              className={`w-full p-4 rounded-lg border text-left transition-colors relative ${
                selectedLicense === index 
                  ? 'border-primary bg-primary/5' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {license.recommended && (
                <Badge className="absolute -top-2 left-4 text-xs">Recommended</Badge>
              )}
              <div className="flex justify-between items-start mb-3">
                <span className="font-semibold text-lg">{license.name}</span>
                <span className="font-bold text-primary text-lg">{license.price}</span>
              </div>
              <ul className="space-y-2 text-sm">
                {license.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </button>
          ))}
        </div>

        <Button size="lg" className="w-full mb-3">
          Purchase ({licenses[selectedLicense].price})
        </Button>
        
        <Button variant="outline" size="lg" className="w-full">
          Live Demo
        </Button>
      </CardContent>
    </Card>
  );
};
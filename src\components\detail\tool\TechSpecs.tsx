import React from 'react';
import { Card, CardContent } from "@/components/ui/card";

interface TechSpecsProps {
  specs: {
    language: string;
    database: string;
    framework: string;
    frontend: string;
    responsive: string;
    browsers: string;
  };
}

export const TechSpecs = ({ specs }: TechSpecsProps) => {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Technical Requirements</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Language:</span>
                <span className="font-medium">{specs.language}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Database:</span>
                <span className="font-medium">{specs.database}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Framework:</span>
                <span className="font-medium">{specs.framework}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Frontend:</span>
                <span className="font-medium">{specs.frontend}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Responsive:</span>
                <span className="font-medium">{specs.responsive}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Browser Support:</span>
                <span className="font-medium">{specs.browsers}</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Installation</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">1</div>
                <p>Upload files to your web server</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">2</div>
                <p>Import MySQL database</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">3</div>
                <p>Configure database settings</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">4</div>
                <p>Run the application</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
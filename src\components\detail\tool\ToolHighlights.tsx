import React from 'react';
import { Code, Database, Download } from 'lucide-react';

export const ToolHighlights = () => {
  const highlights = [
    {
      icon: Code,
      title: "Clean Code",
      description: "Well-structured and documented"
    },
    {
      icon: Database,
      title: "Database Included",
      description: "Complete MySQL structure"
    },
    {
      icon: Download,
      title: "Instant Download",
      description: "Get files immediately"
    }
  ];

  return (
    <div className="grid md:grid-cols-3 gap-6 mb-8">
      {highlights.map((highlight, index) => (
        <div key={index} className="text-center">
          <div className="w-12 h-12 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-3">
            <highlight.icon className="w-6 h-6" />
          </div>
          <h3 className="font-semibold mb-1">{highlight.title}</h3>
          <p className="text-sm text-gray-600">{highlight.description}</p>
        </div>
      ))}
    </div>
  );
};
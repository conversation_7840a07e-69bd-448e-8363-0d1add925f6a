import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Users, Zap, Crown, Info } from 'lucide-react';

interface LicenseOption {
  id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
  limitations?: string[];
  popular?: boolean;
  recommended?: boolean;
}

interface ToolLicensesProps {
  tool: any;
  onSelectLicense: (licenseId: string, price: number) => void;
  className?: string;
}

export const ToolLicenses = ({ tool, onSelectLicense, className = "" }: ToolLicensesProps) => {
  const [selectedLicense, setSelectedLicense] = useState<string>('standard');

  // Generate license options based on tool data
  const getLicenseOptions = (): LicenseOption[] => {
    const basePrice = tool.price || 99;
    
    return [
      {
        id: 'personal',
        name: 'Personal License',
        description: 'Perfect for individual use and personal projects',
        price: Math.round(basePrice * 0.7),
        features: [
          'Single user license',
          'Personal projects only',
          'Email support',
          '1 year of updates',
          'Basic documentation'
        ],
        limitations: [
          'No commercial use',
          'No team sharing',
          'Limited API calls (if applicable)'
        ]
      },
      {
        id: 'standard',
        name: 'Standard License',
        description: 'Great for small businesses and commercial projects',
        price: basePrice,
        features: [
          'Commercial use allowed',
          'Up to 5 team members',
          'Priority email support',
          '2 years of updates',
          'Complete documentation',
          'API access (if applicable)'
        ],
        popular: true
      },
      {
        id: 'premium',
        name: 'Premium License',
        description: 'Best for agencies and larger teams',
        price: Math.round(basePrice * 1.8),
        features: [
          'Unlimited commercial use',
          'Unlimited team members',
          'Priority support + phone',
          'Lifetime updates',
          'Source code access',
          'Custom integrations',
          'White-label rights'
        ],
        recommended: true
      }
    ];
  };

  const licenseOptions = getLicenseOptions();
  const selectedOption = licenseOptions.find(opt => opt.id === selectedLicense);

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Choose Your License</h3>
        <p className="text-gray-600 text-sm">
          Select the license that best fits your needs and usage requirements.
        </p>
      </div>

      {/* License Options */}
      <div className="space-y-4">
        {licenseOptions.map((license) => (
          <div
            key={license.id}
            className={`relative border rounded-lg p-6 cursor-pointer transition-all ${
              selectedLicense === license.id
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
            }`}
            onClick={() => setSelectedLicense(license.id)}
          >
            {/* Badges */}
            <div className="absolute top-4 right-4 flex gap-2">
              {license.popular && (
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  <Users className="w-3 h-3 mr-1" />
                  Popular
                </Badge>
              )}
              {license.recommended && (
                <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                  <Crown className="w-3 h-3 mr-1" />
                  Recommended
                </Badge>
              )}
            </div>

            {/* License Header */}
            <div className="mb-4">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedLicense === license.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`}>
                  {selectedLicense === license.id && (
                    <CheckCircle className="w-4 h-4 text-white -m-0.5" />
                  )}
                </div>
                <h4 className="text-lg font-semibold text-gray-900">{license.name}</h4>
                <div className="text-2xl font-bold text-gray-900">${license.price}</div>
              </div>
              <p className="text-gray-600 text-sm ml-7">{license.description}</p>
            </div>

            {/* Features */}
            <div className="ml-7">
              <h5 className="font-medium text-gray-900 mb-2">What's included:</h5>
              <div className="grid md:grid-cols-2 gap-2 mb-4">
                {license.features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Limitations */}
              {license.limitations && license.limitations.length > 0 && (
                <div>
                  <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                    <Info className="w-4 h-4" />
                    Limitations:
                  </h5>
                  <div className="space-y-1">
                    {license.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm text-gray-600">
                        <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                        <span>{limitation}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* License Comparison */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 mb-2">Need help choosing?</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Personal:</strong> Individual developers and personal projects</p>
          <p><strong>Standard:</strong> Small businesses and commercial applications</p>
          <p><strong>Premium:</strong> Agencies, large teams, and enterprise use</p>
        </div>
      </div>

      {/* Selected License Summary */}
      {selectedOption && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-semibold text-blue-900">
                Selected: {selectedOption.name}
              </h4>
              <p className="text-blue-700 text-sm">{selectedOption.description}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-900">${selectedOption.price}</div>
              <Button 
                onClick={() => onSelectLicense(selectedOption.id, selectedOption.price)}
                className="mt-2 bg-blue-600 hover:bg-blue-700"
              >
                Get {selectedOption.name}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

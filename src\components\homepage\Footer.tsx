import { Link } from 'react-router-dom';
import { useSiteSetting } from '@/hooks/useSupabaseData';

export const Footer = () => {
  const siteName = useSiteSetting('site_name');
  const footerText = useSiteSetting('footer_text');
  const socialFacebook = useSiteSetting('social_facebook');
  const socialTwitter = useSiteSetting('social_twitter');
  const socialLinkedin = useSiteSetting('social_linkedin');
  const socialInstagram = useSiteSetting('social_instagram');
  const supportEmail = useSiteSetting('support_email');
  const contactEmail = useSiteSetting('contact_email');

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <div className="bg-gradient-primary text-white p-2 rounded-lg mr-3">
                <i className="fas fa-rocket text-xl"></i>
              </div>
              <span className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                {siteName || 'EpicFreelancer'}
              </span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              The ultimate marketplace for digital products, services, and micro SaaS tools. 
              Connect with talented creators and find the perfect digital solutions.
            </p>
            <div className="flex space-x-4">
              {socialFacebook && (
                <a href={socialFacebook} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-facebook text-xl"></i>
                </a>
              )}
              {socialTwitter && (
                <a href={socialTwitter} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-twitter text-xl"></i>
                </a>
              )}
              {socialLinkedin && (
                <a href={socialLinkedin} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-linkedin text-xl"></i>
                </a>
              )}
              {socialInstagram && (
                <a href={socialInstagram} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-instagram text-xl"></i>
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/digital-products" className="text-gray-300 hover:text-white transition-colors">Digital Products</Link></li>
              <li><Link to="/services" className="text-gray-300 hover:text-white transition-colors">Services</Link></li>
              <li><Link to="/tools" className="text-gray-300 hover:text-white transition-colors">Tools</Link></li>
              <li><Link to="/how-it-works" className="text-gray-300 hover:text-white transition-colors">How It Works</Link></li>
              <li><Link to="/support" className="text-gray-300 hover:text-white transition-colors">Support</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <ul className="space-y-2">
              {supportEmail && (
                <li>
                  <a href={`mailto:${supportEmail}`} className="text-gray-300 hover:text-white transition-colors">
                    <i className="fas fa-envelope mr-2"></i>
                    Support
                  </a>
                </li>
              )}
              {contactEmail && (
                <li>
                  <a href={`mailto:${contactEmail}`} className="text-gray-300 hover:text-white transition-colors">
                    <i className="fas fa-envelope mr-2"></i>
                    Contact
                  </a>
                </li>
              )}
              <li><Link to="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">Terms of Service</Link></li>
              <li><Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            {footerText || `© 2024 ${siteName || 'EpicFreelancer'}. All rights reserved.`}
          </p>
        </div>
      </div>
    </footer>
  );
};
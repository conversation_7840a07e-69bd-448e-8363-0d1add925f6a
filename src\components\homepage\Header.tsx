import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from "@/contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { Bell } from "lucide-react";
import { useNotification } from '@/contexts/NotificationContext';
import { useSiteSetting } from '@/hooks/useSupabaseData';
import React from 'react';

interface Notification {
  id: number;
  title: string;
  description: string;
  messageId?: number;
  read?: boolean;
}
interface HeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

export const Header = ({ searchQuery, setSearchQuery }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showNotif, setShowNotif] = useState(false);
  const [logoError, setLogoError] = useState(false);
  const navigate = useNavigate();
  const { user, loading, signOut, profile } = useAuth();
  console.log(">>>>>>>>>>> profile", profile)
  const { toast } = useToast();
  const { notifications, unreadCount, markAsRead, setOnNotificationClick } = useNotification();
  const siteName = useSiteSetting('site_name');
  const logoUrl = useSiteSetting('logo_url');

  const handleSellClick = () => {
    if (!user) {
      // Redirect to login if not authenticated
      navigate('/login');
      return;
    }

    // Check if user is already a seller
    if (profile?.is_seller) {
      // If seller, go to seller dashboard
      navigate('/seller-dashboard');
    } else {
      // If buyer, go to seller onboarding
      navigate('/seller-onboarding');
    }
  };

  const handleLogout = async () => {
    try {
      const { error } = await signOut();
      if (error) {
        toast({
          title: "Logout failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });

      navigate('/');
    } catch (error: any) {
      toast({
        title: "Logout failed",
        description: error.message || "An error occurred during logout.",
        variant: "destructive",
      });
    }
  };

  const getUserInitials = () => {
    if (!user?.user_metadata?.full_name) return 'U';
    const names = user.user_metadata.full_name.split(' ');
    return names.length > 1
      ? `${names[0][0]}${names[1][0]}`.toUpperCase()
      : names[0][0].toUpperCase();
  };

  const getUserDisplayName = () => {
    return user?.user_metadata?.full_name || user?.email || 'User';
  };

  console.log("user metadata",profile, user);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center cursor-pointer">
            <div className="bg-gradient-primary text-white p-2 rounded-lg mr-3">
              {logoUrl && !logoError ? (
                <img 
                  src={logoUrl} 
                  alt={siteName} 
                  className="w-6 h-6" 
                  onError={() => setLogoError(true)}
                />
              ) : (
                <i className="fas fa-rocket text-xl"></i>
              )}
            </div>
            <span className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              {siteName || 'EpicFreelancer'}
            </span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <div className="relative group">
              <Button variant="ghost" className="whitespace-nowrap">
                Browse <i className="fas fa-chevron-down ml-1"></i>
              </Button>
              <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-2">
                  <Link to="/digital-products" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Digital Products</Link>
                  <Link to="/services" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Services</Link>
                  <Link to="/micro-saas" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Micro SaaS</Link>
                </div>
              </div>
            </div>
            
            
            
            {/* Only show 'Sell on Platform' if user is logged in and not a seller */}
            {user && profile && !profile.is_seller && (
              <Button 
                variant="ghost" 
                className="whitespace-nowrap text-primary font-medium hover:bg-primary/10" 
                onClick={handleSellClick}
              >
                Sell on Platform
              </Button>
            )}
            <Button variant="ghost" className="whitespace-nowrap" asChild>
              <Link to="/how-it-works">How It Works</Link>
            </Button>
            <Button variant="ghost" className="whitespace-nowrap" asChild>
              <Link to="/support">Support</Link>
            </Button>
          </nav>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <form
              onSubmit={e => {
                e.preventDefault();
                if (searchQuery.trim()) {
                  navigate(`/search?query=${encodeURIComponent(searchQuery.trim())}`);
                }
              }}
            >
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Search for products, services, or tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border-gray-300 rounded-lg text-sm"
                />
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </form>
          </div>

          {/* Auth Section */}
          <div className="flex items-center space-x-3">
            {/* Notification Bell */}
            <div className="relative">
              <Button variant="ghost" className="p-2" onClick={() => setShowNotif((v) => !v)}>
                <Bell className="w-6 h-6" />
                {unreadCount > 0 && (
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                )}
              </Button>
              {showNotif && (
                <div className="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg z-50">
                  <div className="p-3 font-semibold border-b">Notifications</div>
                  {notifications.length === 0 ? (
                    <div className="p-4 text-gray-500 text-sm">No notifications</div>
                  ) : (
                    notifications.map((notif) => (
                      <div
                        key={notif.id}
                        className={`px-4 py-3 cursor-pointer hover:bg-gray-100 ${!notif.read ? 'bg-blue-50' : ''}`}
                        onClick={() => {
                          setShowNotif(false);
                          markAsRead(notif.id);
                          // Use the notification click handler
                          if (profile?.is_seller) {
                            navigate('/seller-dashboard?tab=messages');
                          } else {
                            navigate('/buyer-dashboard?tab=messages');
                          }
                        }}
                      >
                        <div className="font-medium">{notif.title}</div>
                        <div className="text-xs text-gray-600">{notif.description}</div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
            {loading ? (
              // Loading state
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ) : user ? (
              // Authenticated user menu
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 p-2">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="text-sm">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden md:block text-sm font-medium">
                      {getUserDisplayName()}
                    </span>
                    <i className="fas fa-chevron-down text-xs"></i>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link to={profile?.is_seller ? "/seller-dashboard" : "/buyer-dashboard"} className="flex items-center">
                      <i className="fas fa-tachometer-alt mr-2"></i>
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {profile?.is_admin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center text-red-600">
                        <i className="fas fa-shield-alt mr-2"></i>
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem asChild>
                    <Link to="/profile" className="flex items-center">
                      <i className="fas fa-user mr-2"></i>
                      Profile Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/orders" className="flex items-center">
                      <i className="fas fa-shopping-bag mr-2"></i>
                      My Orders
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="flex items-center text-red-600 focus:text-red-600"
                  >
                    <i className="fas fa-sign-out-alt mr-2"></i>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              // Not authenticated - show login/register buttons
              <>
                <Button variant="outline" className="whitespace-nowrap" asChild>
                  <Link to="/login">Login</Link>
                </Button>
                <Button className="whitespace-nowrap" asChild>
                  <Link to="/register">Register</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <i className="fas fa-bars"></i>
          </Button>
        </div>
      </div>
    </header>
  );
};

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link, useNavigate } from "react-router-dom";
import heroBg from '@/assets/hero-workspace.jpg';
import { useState } from 'react';
import { useSiteSetting } from '@/hooks/useSupabaseData';

export const HeroSection = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  const heroTitle = useSiteSetting('hero_title');
  const heroSubtitle = useSiteSetting('hero_subtitle');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?query=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <section 
      className="relative bg-gradient-hero text-white py-20"
      style={{
        backgroundImage: `url('${heroBg}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      
      
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-700/90"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl md:text-6xl font-bold mb-6">
          {heroTitle || <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Your Gateway to
            <br />
            <span className="text-yellow-300">Epic Freelancing</span>
          </h1>}
        </h1>
        <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
          {heroSubtitle ||  <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Discover premium digital products, professional services, and innovative micro SaaS tools all in one marketplace
          </p>}
        </p>
        
        {/* Search Bar */}
        <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
          <div className="flex bg-white rounded-lg overflow-hidden shadow-lg">
            <Input
              type="text"
              placeholder="Search for products, services, or tools..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 border-0 focus:ring-0 text-gray-900 px-6 py-4 text-lg"
            />
            <Button type="submit" className="bg-primary hover:bg-primary/90 px-8 py-4 rounded-none">
              <i className="fas fa-search mr-2"></i>
              Search
            </Button>
          </div>
        </form>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-primary hover:bg-primary/90 px-8 py-4 text-lg" asChild>
            <Link to="/digital-products">
              Browse Products
            </Link>
          </Button>
          <Button size="lg" variant="outline" className="border-white text-white bg-transparent hover:bg-white hover:text-gray-900 px-8 py-4 text-lg" asChild>
            <Link to="/how-it-works">
              How It Works
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};
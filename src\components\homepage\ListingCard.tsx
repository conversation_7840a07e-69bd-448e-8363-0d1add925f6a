import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Link } from "react-router-dom";

interface Listing {
  id: string;
  title: string;
  seller: string;
  price: string;
  rating: number;
  reviews: number;
  category?: string;
  image: string;
  image_url: string;
  verified?: boolean;
  timeAgo?: string;
  review_count?: number;
}

interface ListingCardProps {
  listing: Listing;
  size?: 'small' | 'medium' | 'large';
}

const renderStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => (
    <i
      key={i}
      className={`fas fa-star text-sm ${
        i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
      }`}
    />
  ));
};

export const ListingCard = ({ listing, size = 'medium' }: ListingCardProps) => {
  const cardClasses = {
    small: 'w-72',
    medium: 'w-80',
    large: 'w-96'
  };

  

  return (
    <Link to={`/product/${listing.slug || listing.id}`}>
      <Card className={`${cardClasses[size]} hover:shadow-lg transition-shadow duration-300 cursor-pointer group`}>
        <div className="relative overflow-hidden">
          <img
            src={listing?.image_url}
            alt={listing?.title}
            className="w-full h-48 object-cover object-top group-hover:scale-105 transition-transform duration-300"
          />
          {listing?.verified && (
            <Badge className="absolute top-2 right-2 bg-green-500 text-white">
              <i className="fas fa-check mr-1"></i>
              Verified
            </Badge>
          )}
          <Badge className="absolute top-2 left-2 bg-blue-600 text-white">
            {listing?.category || 'Featured'}
          </Badge>
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {listing?.title}
          </h3>
          <div className="flex items-center mb-2">
            <Avatar className="w-6 h-6 mr-2">
              <AvatarFallback className="text-xs">{listing?.seller?.[0]}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-gray-600">{listing?.seller}</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {renderStars(listing?.rating)}
              <span className="ml-1 text-sm text-gray-600">({listing?.reviews})</span>
            </div>
            <span className="font-bold text-lg text-green-600">{listing?.price}</span>
          </div>
          {listing?.timeAgo && (
            <div className="mt-2">
              <Badge variant="outline" className="text-xs">
                <i className="fas fa-clock mr-1"></i>
                {listing?.timeAgo}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
};
import React from 'react';
import { Button } from "@/components/ui/button";
import { ListingCard } from './ListingCard';
import { useListingsByType } from '@/hooks/useSupabaseData';
import { Link } from 'react-router-dom';

// Transform Supabase data to ListingCard format
const transformListing = (listing: any) => ({
  id: listing.id,
  slug: listing.slug,
  title: listing.title,
  seller: listing.seller_name || 'Unknown Seller',
  price: listing.price ? `$${listing.price}` : 'Free',
  rating: listing.rating || 0,
  reviews: listing.review_count || 0,
  category: listing.categories?.name,
  image: listing.image_url || '/placeholder-image.jpg',
  image_url: listing.image_url || '/placeholder-image.jpg',
  verified: listing.featured || false,
  type: listing.type || 'product',
});

export const MicroSaasSection = () => {
  const { data: listings, isLoading, error } = useListingsByType('tool', 3);

  if (isLoading) {
    return (
      <section className="py-16 bg-gradient-to-br from-green-50 to-teal-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              <i className="fas fa-cogs text-teal-500 mr-3"></i>
              Micro SaaS & Tools
            </h2>
            <p className="text-xl text-gray-600">Powerful software solutions to streamline your workflow</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center mb-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="w-80 h-64 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 bg-gradient-to-br from-green-50 to-teal-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600">Error loading tools</p>
          </div>
        </div>
      </section>
    );
  }

  const transformedListings = listings?.map(transformListing) || [];

  return (
    <section className="py-16 bg-gradient-to-br from-green-50 to-teal-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            <i className="fas fa-cogs text-teal-500 mr-3"></i>
            Micro SaaS & Tools
          </h2>
          <p className="text-xl text-gray-600">Powerful software solutions to streamline your workflow</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center mb-8">
          {transformedListings.map((listing) => (
            <ListingCard key={listing.id} listing={listing} size="medium" />
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" className="whitespace-nowrap" asChild>
            <Link to="/tools">
              Discover All Tools <i className="fas fa-arrow-right ml-2"></i>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};
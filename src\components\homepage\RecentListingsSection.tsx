import { Button } from "@/components/ui/button";
import { ListingCard } from './ListingCard';
import { useRecentListings } from '@/hooks/useSupabaseData';

// Transform Supabase data to ListingCard format
const transformListing = (listing: any) => {
  const createdAt = new Date(listing.created_at);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60));

  let timeAgo = '';
  if (diffInHours < 1) {
    timeAgo = 'Just now';
  } else if (diffInHours < 24) {
    timeAgo = `${diffInHours} hours ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    timeAgo = `${diffInDays} days ago`;
  }

  return {
    id: listing.id,
    title: listing.title,
    seller: listing.seller_name || 'Unknown Seller',
    price: listing.price ? `$${listing.price}` : 'Free',
    rating: listing.rating || 0,
    reviews: listing.review_count || 0,
    category: listing.categories?.name,
    image: listing.image_url || '/placeholder-image.jpg',
    image_url: listing.image_url || '/placeholder-image.jpg',
    timeAgo,
    type: listing.type || 'product',
  };
};

export const RecentListingsSection = () => {
  const { data: listings, isLoading, error } = useRecentListings(3);
  console.log("listings", listings);

  if (isLoading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">
              <i className="fas fa-clock text-blue-600 mr-3"></i>
              Recently Posted
            </h2>
            <Button variant="outline" className="whitespace-nowrap">
              View All <i className="fas fa-arrow-right ml-2"></i>
            </Button>
          </div>
          <div className="flex gap-6 overflow-x-auto pb-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex-shrink-0 w-80 h-64 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600">Error loading recent listings</p>
          </div>
        </div>
      </section>
    );
  }

  const transformedListings = listings?.map(transformListing) || [];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-gray-900">
            <i className="fas fa-clock text-blue-600 mr-3"></i>
            Recently Posted
          </h2>
          <Button variant="outline" className="whitespace-nowrap">
            View All <i className="fas fa-arrow-right ml-2"></i>
          </Button>
        </div>

        <div className="flex gap-6 overflow-x-auto pb-4">
          {transformedListings.map((listing) => (
            <div key={listing.id} className="flex-shrink-0">
              <ListingCard listing={listing} size="medium" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
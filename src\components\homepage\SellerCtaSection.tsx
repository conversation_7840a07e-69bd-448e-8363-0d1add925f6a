import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import sellerImage from '@/assets/seller-cta.jpg';

export const SellerCtaSection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleStartSelling = () => {
    if (!user) {
      // If not logged in, redirect to login
      navigate('/login');
      return;
    }

    // Check if user is already a seller
    if (user.user_metadata?.is_seller) {
      // If seller, go to product creation
      navigate('/seller-dashboard');
    } else {
      // If buyer, go to seller onboarding
      navigate('/seller-onboarding');
    }
  };

  return (
    <section className="py-20 bg-gradient-hero text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Start Selling?
            </h2>
            <p className="text-xl mb-8 text-blue-100">
              Join thousands of successful freelancers and entrepreneurs who are already earning on our platform. 
              Start your journey today and turn your skills into profit.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-300">50K+</div>
                <div className="text-blue-100">Active Sellers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-300">$2M+</div>
                <div className="text-blue-100">Total Earnings</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-300">4.8★</div>
                <div className="text-blue-100">Average Rating</div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-gray-100 whitespace-nowrap"
                onClick={handleStartSelling}
              >
                <i className="fas fa-rocket mr-2"></i>
                Start Selling Now
              </Button>
              <Button size="lg" variant="hero" className="whitespace-nowrap" asChild>
                <Link to="/how-it-works">
                  <i className="fas fa-play mr-2"></i>
                  Watch How It Works
                </Link>
              </Button>
            </div>
          </div>
          
          <div className="relative">
            <img
              src={sellerImage}
              alt="Successful Seller"
              className="w-full h-auto rounded-lg shadow-2xl object-cover object-top"
            />
            <div className="absolute -top-4 -right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg">
              <div className="text-2xl font-bold">$5,247</div>
              <div className="text-sm">This Month</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
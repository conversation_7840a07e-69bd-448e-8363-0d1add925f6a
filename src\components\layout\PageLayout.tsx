import { useState } from 'react';
import { Header } from '@/components/homepage/Header';
import { Footer } from '@/components/homepage/Footer';

interface PageLayoutProps {
  children: React.ReactNode;
}

export const PageLayout = ({ children }: PageLayoutProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-between">
      <Header searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      <main>{children}</main>
      <Footer />
    </div>
  );
};
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';

interface NavigationButtonsProps {
  currentStep: number;
  totalSteps: number;
  onPrevious: () => void;
  onNext: () => void;
  onSkip?: () => void;
  nextDisabled?: boolean;
  nextLabel?: string;
  showSkip?: boolean;
}

const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSkip,
  nextDisabled = false,
  nextLabel,
  showSkip = false
}) => {
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  return (
    <div className="flex justify-between items-center mt-8 pt-6 border-t">
      <div>
        {!isFirstStep && (
          <Button variant="outline" onClick={onPrevious}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>
        )}
      </div>

      <div className="flex space-x-3">
        {showSkip && !isLastStep && (
          <Button variant="ghost" onClick={onSkip}>
            Skip for now
          </Button>
        )}
        
        <Button 
          onClick={onNext} 
          disabled={nextDisabled}
          className="min-w-24"
        >
          {nextLabel || (isLastStep ? 'Complete' : 'Next')}
          {!isLastStep && <ArrowRight className="w-4 h-4 ml-2" />}
        </Button>
      </div>
    </div>
  );
};

export default NavigationButtons;
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Store } from "lucide-react";
import { useNavigate } from 'react-router-dom';

export const OnboardingHeader = () => {
  const navigate = useNavigate();

  return (
    <div className="bg-white shadow-sm">
      <div className="max-w-2xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="flex items-center text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Homepage
          </Button>
          
          <div className="flex items-center space-x-2">
            <div className="bg-gradient-primary text-white p-2 rounded-lg">
              <Store className="h-5 w-5" />
            </div>
            <span className="font-semibold text-lg">Become a Seller</span>
          </div>
        </div>
      </div>
    </div>
  );
};

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Clock, AlertCircle, User, FileText, CreditCard, Package, Star, Shield } from 'lucide-react';

interface ApprovalProcessProps {
  data: any;
  updateData: (data: any) => void;
}

const ApprovalProcess: React.FC<ApprovalProcessProps> = ({ data, updateData }) => {
  const checklistItems = [
    {
      id: 'profile',
      title: 'Profile Complete',
      description: 'Personal information and professional bio',
      icon: User,
      completed: !!(data.fullName && data.email && data.bio),
      required: true
    },
    {
      id: 'portfolio',
      title: 'Portfolio Setup',
      description: 'Skills, experience, and portfolio samples',
      icon: Star,
      completed: !!(data.displayName && data.title && data.skills?.length > 0),
      required: true
    },
    {
      id: 'marketplace',
      title: 'Marketplace Selection',
      description: 'Choose what type of products/services to sell',
      icon: Package,
      completed: !!(data.marketplaceTypes?.length > 0),
      required: true
    },
    {
      id: 'payment',
      title: 'Payment Setup',
      description: 'Configure how you\'ll receive payments',
      icon: CreditCard,
      completed: !!(data.paymentMethod),
      required: true
    },
    {
      id: 'listing',
      title: 'First Listing Created',
      description: 'At least one listing ready for review',
      icon: FileText,
      completed: !!(data.listingTitle && data.listingDescription && data.listingPrice),
      required: true
    },
    {
      id: 'verification',
      title: 'Identity Verification',
      description: 'Government ID and profile photo uploaded',
      icon: Shield,
      completed: false, // This would be checked server-side
      required: true
    }
  ];

  const completedCount = checklistItems.filter(item => item.completed).length;
  const allCompleted = completedCount === checklistItems.length;

  const expectedTimeline = [
    {
      step: 'Submission Review',
      duration: '24-48 hours',
      description: 'Our team reviews your profile and first listing'
    },
    {
      step: 'Identity Verification',
      duration: '1-2 business days',
      description: 'Verification of your uploaded documents'
    },
    {
      step: 'Quality Assessment',
      duration: '1-3 business days',
      description: 'Review of your portfolio and listing quality'
    },
    {
      step: 'Account Activation',
      duration: 'Immediate',
      description: 'Your seller account goes live on the platform'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Approval Process</h2>
        <p className="text-gray-600">Review your submission and track your approval status</p>
      </div>

      <Card className={allCompleted ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              {allCompleted ? (
                <Check className="w-6 h-6 text-green-600 mr-3" />
              ) : (
                <Clock className="w-6 h-6 text-orange-600 mr-3" />
              )}
              <div>
                <h3 className="font-semibold text-lg">
                  {allCompleted ? 'Ready for Submission!' : 'Complete Your Profile'}
                </h3>
                <p className="text-sm text-gray-600">
                  {allCompleted 
                    ? 'All requirements met. You can submit for approval.' 
                    : `${completedCount}/${checklistItems.length} requirements completed`
                  }
                </p>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold">
                {Math.round((completedCount / checklistItems.length) * 100)}%
              </div>
              <div className="text-sm text-gray-500">Complete</div>
            </div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                allCompleted ? 'bg-green-500' : 'bg-orange-500'
              }`}
              style={{ width: `${(completedCount / checklistItems.length) * 100}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Submission Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {checklistItems.map((item) => {
              const Icon = item.icon;
              
              return (
                <div key={item.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                  <div className={`p-2 rounded-full ${
                    item.completed 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-gray-100 text-gray-400'
                  }`}>
                    {item.completed ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Icon className="w-4 h-4" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{item.title}</h4>
                      <Badge variant={item.completed ? "default" : "secondary"}>
                        {item.completed ? 'Complete' : 'Pending'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Approval Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {expectedTimeline.map((phase, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex flex-col items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === 0 ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
                  }`}>
                    {index + 1}
                  </div>
                  {index < expectedTimeline.length - 1 && (
                    <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>
                  )}
                </div>
                
                <div className="flex-1 pb-6">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium">{phase.step}</h4>
                    <Badge variant="outline" className="text-xs">
                      {phase.duration}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{phase.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2 text-blue-600" />
              What Happens Next?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p className="text-sm">You'll receive an email confirmation of your submission</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p className="text-sm">Our team will review your profile and listing within 48 hours</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p className="text-sm">We'll notify you once your account is approved and live</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p className="text-sm">You can start receiving orders immediately after approval</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="w-5 h-5 mr-2 text-green-600" />
              Seller Benefits
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start space-x-3">
              <Check className="w-4 h-4 text-green-600 mt-0.5" />
              <p className="text-sm">Access to millions of potential buyers</p>
            </div>
            <div className="flex items-start space-x-3">
              <Check className="w-4 h-4 text-green-600 mt-0.5" />
              <p className="text-sm">Secure payment processing and buyer protection</p>
            </div>
            <div className="flex items-start space-x-3">
              <Check className="w-4 h-4 text-green-600 mt-0.5" />
              <p className="text-sm">24/7 seller support and dispute resolution</p>
            </div>
            <div className="flex items-start space-x-3">
              <Check className="w-4 h-4 text-green-600 mt-0.5" />
              <p className="text-sm">Analytics and performance tracking tools</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {!allCompleted && (
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
            <div>
              <h4 className="font-medium text-yellow-900">Complete Missing Requirements</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Please go back and complete all required sections before submitting for approval. 
                Use the navigation buttons to revisit previous steps.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApprovalProcess;
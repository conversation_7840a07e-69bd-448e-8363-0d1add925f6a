import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Store } from "lucide-react";
import React, { useState, forwardRef, useImperativeHandle } from 'react';

interface BusinessProfileProps {
  formData: {
    businessName: string;
    description: string;
    category: string;
  };
  categories: { id: string; name: string }[];
  onInputChange: (field: string, value: string) => void;
}

export const BusinessProfile = forwardRef(function BusinessProfile({ formData, categories, onInputChange }: BusinessProfileProps, ref) {
  const [errors, setErrors] = useState<{businessName?: string; description?: string; category?: string}>({});

  const validate = () => {
    const newErrors: typeof errors = {};
    if (!formData.businessName) newErrors.businessName = 'Business/Brand Name is required.';
    if (!formData.description) newErrors.description = 'Description is required.';
    if (!formData.category) newErrors.category = 'Category is required.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  useImperativeHandle(ref, () => ({ validate }));

  // You can call validate() on next/submit button click in the parent
  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Store className="h-12 w-12 text-primary" />
        </div>
        <CardTitle>Tell us about your business</CardTitle>
        <CardDescription>
          Let's set up your seller profile in just a few simple steps
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="businessName">Business/Brand Name *</Label>
          <Input
            id="businessName"
            placeholder="e.g., Amazing Designs Co."
            value={formData.businessName}
            onChange={(e) => onInputChange('businessName', e.target.value)}
          />
          {errors.businessName && <p className="text-red-500 text-xs mt-1">{errors.businessName}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="description">What do you offer? *</Label>
          <Textarea
            id="description"
            placeholder="Briefly describe your products or services..."
            value={formData.description}
            onChange={(e) => onInputChange('description', e.target.value)}
            rows={3}
          />
          {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="category">Main Category *</Label>
          <Select value={formData.category} onValueChange={(value) => onInputChange('category', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Choose your main category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((cat) => (
                <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
        </div>
      </CardContent>
    </Card>
  );
});

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React, { useState, forwardRef, useImperativeHandle } from 'react';

interface ContactInfoProps {
  formData: {
    contactEmail: string;
    phone: string;
  };
  onInputChange: (field: string, value: string) => void;
}

export const ContactInfo = forwardRef(function ContactInfo({ formData, onInputChange }: ContactInfoProps, ref) {
  const [error, setError] = useState<string | null>(null);

  const validate = () => {
    if (!formData.contactEmail) {
      setError('Contact Email is required.');
      return false;
    }
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(formData.contactEmail)) {
      setError('Enter a valid email address.');
      return false;
    }
    setError(null);
    return true;
  };

  useImperativeH<PERSON>le(ref, () => ({ validate }));

  // Call validate() on next/submit in parent
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle>Contact Information</CardTitle>
        <CardDescription>
          How can customers and we reach you?
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="contactEmail">Contact Email *</Label>
          <Input
            id="contactEmail"
            type="email"
            placeholder="<EMAIL>"
            value={formData.contactEmail}
            onChange={(e) => onInputChange('contactEmail', e.target.value)}
          />
          {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="phone">Phone (Optional)</Label>
          <Input
            id="phone"
            placeholder="+****************"
            value={formData.phone}
            onChange={(e) => onInputChange('phone', e.target.value)}
          />
        </div>
      </CardContent>
    </Card>
  );
});

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Upload, Tag, DollarSign, Clock, Star, Package } from 'lucide-react';

interface CreateFirstListingProps {
  data: any;
  updateData: (data: any) => void;
}

const CreateFirstListing: React.FC<CreateFirstListingProps> = ({ data, updateData }) => {
  const [selectedCategory, setSelectedCategory] = useState(data.listingCategory || '');
  const [tags, setTags] = useState<string[]>(data.listingTags || []);
  const [newTag, setNewTag] = useState('');

  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim()) && tags.length < 10) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);
      updateData({ listingTags: updatedTags });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove);
    setTags(updatedTags);
    updateData({ listingTags: updatedTags });
  };

  const categories = [
    { id: 'digital-products', name: 'Digital Products', subcategories: ['Templates', 'Graphics', 'Stock Photos', 'Fonts', 'eBooks'] },
    { id: 'services', name: 'Services', subcategories: ['Web Development', 'Design', 'Writing', 'Marketing', 'Consulting'] },
    { id: 'micro-saas', name: 'Micro SaaS', subcategories: ['Analytics', 'Productivity', 'E-commerce', 'Marketing Tools', 'APIs'] }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create First Listing</h2>
        <p className="text-gray-600">Let's create your first listing to showcase what you offer</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="w-5 h-5 mr-2 text-blue-600" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="listingTitle">Listing Title *</Label>
              <Input
                id="listingTitle"
                value={data.listingTitle || ''}
                onChange={(e) => handleInputChange('listingTitle', e.target.value)}
                placeholder="e.g., Professional Logo Design Package"
                maxLength={80}
              />
              <p className="text-xs text-gray-500 mt-1">
                {(data.listingTitle || '').length}/80 characters
              </p>
            </div>

            <div>
              <Label htmlFor="listingCategory">Category *</Label>
              <select
                id="listingCategory"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  handleInputChange('listingCategory', e.target.value);
                }}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <optgroup key={category.id} label={category.name}>
                    {category.subcategories.map((sub) => (
                      <option key={sub} value={`${category.id}-${sub.toLowerCase().replace(' ', '-')}`}>
                        {sub}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="listingDescription">Description *</Label>
              <Textarea
                id="listingDescription"
                value={data.listingDescription || ''}
                onChange={(e) => handleInputChange('listingDescription', e.target.value)}
                placeholder="Describe what you're offering, what's included, and why buyers should choose you..."
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-gray-500 mt-1">
                {(data.listingDescription || '').length}/1000 characters
              </p>
            </div>

            <div>
              <Label>Tags (up to 10)</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add relevant tags"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button onClick={addTag} size="sm" disabled={tags.length >= 10}>
                  <Tag className="w-4 h-4" />
                </Button>
              </div>
              
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="ml-1 text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="w-5 h-5 mr-2 text-green-600" />
              Pricing & Delivery
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="listingPrice">Price *</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  <Input
                    id="listingPrice"
                    type="number"
                    className="pl-8"
                    value={data.listingPrice || ''}
                    onChange={(e) => handleInputChange('listingPrice', e.target.value)}
                    placeholder="99"
                    min="5"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="deliveryTime">Delivery Time *</Label>
                <select
                  id="deliveryTime"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={data.deliveryTime || ''}
                  onChange={(e) => handleInputChange('deliveryTime', e.target.value)}
                >
                  <option value="">Select delivery time</option>
                  <option value="1">1 day</option>
                  <option value="3">3 days</option>
                  <option value="7">1 week</option>
                  <option value="14">2 weeks</option>
                  <option value="30">1 month</option>
                </select>
              </div>
            </div>

            <div>
              <Label htmlFor="revisions">Number of Revisions</Label>
              <select
                id="revisions"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={data.revisions || ''}
                onChange={(e) => handleInputChange('revisions', e.target.value)}
              >
                <option value="">Select revisions</option>
                <option value="0">No revisions</option>
                <option value="1">1 revision</option>
                <option value="3">3 revisions</option>
                <option value="5">5 revisions</option>
                <option value="unlimited">Unlimited revisions</option>
              </select>
            </div>

            <div>
              <Label>What's Included</Label>
              <div className="space-y-2">
                {['Source files', 'Commercial license', '24/7 support', 'Money-back guarantee'].map((item) => (
                  <label key={item} className="flex items-center">
                    <input
                      type="checkbox"
                      className="mr-2"
                      checked={(data.included || []).includes(item)}
                      onChange={(e) => {
                        const included = data.included || [];
                        if (e.target.checked) {
                          updateData({ included: [...included, item] });
                        } else {
                          updateData({ included: included.filter((i: string) => i !== item) });
                        }
                      }}
                    />
                    <span className="text-sm">{item}</span>
                  </label>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="w-5 h-5 mr-2 text-purple-600" />
            Listing Images & Files
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer">
              <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">Main Image *</p>
              <p className="text-xs text-gray-500">JPG, PNG up to 5MB</p>
            </div>
            
            {[2, 3].map((index) => (
              <div
                key={index}
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer"
              >
                <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">Image {index}</p>
                <p className="text-xs text-gray-500">Optional</p>
              </div>
            ))}
          </div>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer">
            <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-600">Upload Deliverable Files (Optional)</p>
            <p className="text-xs text-gray-500">PDF, ZIP, etc. - Files buyers will receive</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="w-5 h-5 mr-2 text-yellow-500" />
            Listing Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                <Package className="w-8 h-8 text-gray-400" />
              </div>
              
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">
                  {data.listingTitle || 'Your Listing Title'}
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  {data.listingDescription?.substring(0, 100) || 'Your listing description will appear here...'}
                  {data.listingDescription?.length > 100 && '...'}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{selectedCategory || 'Category'}</Badge>
                    <span className="text-sm text-gray-500">
                      {data.deliveryTime ? `${data.deliveryTime} day delivery` : 'Delivery time'}
                    </span>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600">
                      ${data.listingPrice || '0'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="flex items-start">
          <i className="fas fa-lightbulb text-blue-600 mt-1 mr-3"></i>
          <div>
            <h4 className="font-medium text-blue-900">Listing Tips</h4>
            <p className="text-sm text-blue-700 mt-1">
              Great listings have clear titles, detailed descriptions, competitive pricing, and high-quality images. 
              This helps buyers understand exactly what they're getting.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateFirstListing;
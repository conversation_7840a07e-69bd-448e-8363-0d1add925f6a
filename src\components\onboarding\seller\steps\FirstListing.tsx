import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload } from "lucide-react";
import React, { useState, forwardRef, useImperativeHandle } from 'react';

interface FirstListingProps {
  formData: {
    productTitle: string;
    productDescription: string;
    price: string;
    productCategory: string;
  };
  categories: { id: string; name: string }[];
  onInputChange: (field: string, value: string) => void;
}

export const FirstListing = forwardRef(function FirstListing({ formData, categories, onInputChange }: FirstListingProps, ref) {
  const [errors, setErrors] = useState<{productTitle?: string; productDescription?: string; price?: string; productCategory?: string}>({});

  const validate = () => {
    const newErrors: typeof errors = {};
    if (!formData.productTitle) newErrors.productTitle = 'Title is required.';
    if (!formData.productDescription) newErrors.productDescription = 'Description is required.';
    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) newErrors.price = 'Enter a valid price.';
    if (!formData.productCategory) newErrors.productCategory = 'Category is required.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  useImperativeHandle(ref, () => ({ validate }));

  // Call validate() on next/submit in parent
  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Upload className="h-12 w-12 text-primary" />
        </div>
        <CardTitle>Create Your First Listing</CardTitle>
        <CardDescription>
          Let's create your first product or service listing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="productTitle">Product/Service Title *</Label>
          <Input
            id="productTitle"
            placeholder="e.g., Professional Logo Design"
            value={formData.productTitle}
            onChange={(e) => onInputChange('productTitle', e.target.value)}
          />
          {errors.productTitle && <p className="text-red-500 text-xs mt-1">{errors.productTitle}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="productDescription">Description *</Label>
          <Textarea
            id="productDescription"
            placeholder="Describe what you're offering, what's included, delivery time..."
            value={formData.productDescription}
            onChange={(e) => onInputChange('productDescription', e.target.value)}
            rows={3}
          />
          {errors.productDescription && <p className="text-red-500 text-xs mt-1">{errors.productDescription}</p>}
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="price">Price (USD) *</Label>
            <Input
              id="price"
              placeholder="49.99"
              value={formData.price}
              onChange={(e) => onInputChange('price', e.target.value)}
            />
            {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="productCategory">Category *</Label>
            <Select value={formData.productCategory} onValueChange={(value) => onInputChange('productCategory', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.productCategory && <p className="text-red-500 text-xs mt-1">{errors.productCategory}</p>}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

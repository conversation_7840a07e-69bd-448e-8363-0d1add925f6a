import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Package, Wrench, Zap } from 'lucide-react';

interface MarketplaceTypeSelectionProps {
  data: any;
  updateData: (data: any) => void;
}

const MarketplaceTypeSelection: React.FC<MarketplaceTypeSelectionProps> = ({ data, updateData }) => {
  const marketplaceTypes = [
    {
      id: 'digital-products',
      name: 'Digital Products',
      icon: Package,
      description: 'Sell templates, designs, software, courses, and digital downloads',
      examples: ['Website Templates', 'Logo Designs', 'Stock Photos', 'Online Courses', 'eBooks', 'Mobile Apps'],
      commission: '5%',
      features: ['Instant delivery', 'Digital rights management', 'Version control', 'Customer support'],
      recommended: true
    },
    {
      id: 'services',
      name: 'Professional Services',
      icon: Wrench,
      description: 'Offer consulting, development, design, marketing, and other professional services',
      examples: ['Web Development', 'Graphic Design', 'Content Writing', 'Digital Marketing', 'Consulting', 'Video Editing'],
      commission: '10%',
      features: ['Project management', 'Milestone tracking', 'Communication tools', 'Dispute resolution']
    },
    {
      id: 'micro-saas',
      name: 'Micro SaaS',
      icon: Zap,
      description: 'Launch and sell subscription-based software tools and applications',
      examples: ['Analytics Tools', 'Productivity Apps', 'API Services', 'Automation Tools', 'Dashboard Solutions'],
      commission: '15%',
      features: ['Subscription management', 'Usage analytics', 'API integrations', 'Billing automation'],
      popular: true
    }
  ];

  const selectedTypes = data.marketplaceTypes || [];

  const toggleSelection = (typeId: string) => {
    const currentSelected = selectedTypes.includes(typeId);
    let newSelected;
    
    if (currentSelected) {
      newSelected = selectedTypes.filter((id: string) => id !== typeId);
    } else {
      newSelected = [...selectedTypes, typeId];
    }
    
    updateData({ marketplaceTypes: newSelected });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Marketplace Type</h2>
        <p className="text-gray-600">Select what you want to sell (you can choose multiple categories)</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {marketplaceTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = selectedTypes.includes(type.id);
          
          return (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                isSelected 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : 'hover:ring-1 hover:ring-gray-300'
              }`}
              onClick={() => toggleSelection(type.id)}
            >
              <CardHeader className="relative">
                {type.recommended && (
                  <Badge className="absolute -top-2 -right-2 bg-green-500">
                    Recommended
                  </Badge>
                )}
                {type.popular && (
                  <Badge className="absolute -top-2 -right-2 bg-orange-500">
                    Popular
                  </Badge>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg mr-3 ${
                      type.id === 'digital-products' ? 'bg-blue-100 text-blue-600' :
                      type.id === 'services' ? 'bg-green-100 text-green-600' :
                      'bg-purple-100 text-purple-600'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <CardTitle className="text-lg">{type.name}</CardTitle>
                  </div>
                  
                  {isSelected && (
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <p className="text-gray-600 text-sm">{type.description}</p>
                
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Examples:</h4>
                  <div className="flex flex-wrap gap-1">
                    {type.examples.slice(0, 4).map((example, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {example}
                      </Badge>
                    ))}
                    {type.examples.length > 4 && (
                      <Badge variant="outline" className="text-xs">
                        +{type.examples.length - 4} more
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Features:</h4>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {type.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <Check className="w-3 h-3 text-green-500 mr-1" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Platform Commission:</span>
                    <Badge variant="secondary" className="font-medium">
                      {type.commission}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedTypes.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="flex items-start">
              <Check className="w-5 h-5 text-green-600 mt-0.5 mr-3" />
              <div>
                <h4 className="font-medium text-green-900">Selected Categories</h4>
                <p className="text-sm text-green-700 mt-1">
                  You've selected {selectedTypes.length} category{selectedTypes.length > 1 ? 'ies' : 'y'}. 
                  You can create listings in all selected categories.
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedTypes.map((typeId: string) => {
                    const type = marketplaceTypes.find(t => t.id === typeId);
                    return (
                      <Badge key={typeId} className="bg-green-100 text-green-800">
                        {type?.name}
                      </Badge>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start">
          <i className="fas fa-info-circle text-gray-600 mt-1 mr-3"></i>
          <div>
            <h4 className="font-medium text-gray-900">Multi-Category Benefits</h4>
            <p className="text-sm text-gray-600 mt-1">
              Selecting multiple categories allows you to diversify your offerings and reach more potential buyers. 
              You can always add more categories later from your seller dashboard.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketplaceTypeSelection;
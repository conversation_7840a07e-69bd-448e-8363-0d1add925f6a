import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Building, Smartphone, Globe, Shield, DollarSign } from 'lucide-react';

interface PaymentDetailsSetupProps {
  data: any;
  updateData: (data: any) => void;
}

const PaymentDetailsSetup: React.FC<PaymentDetailsSetupProps> = ({ data, updateData }) => {
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
  };

  const paymentMethods = [
    {
      id: 'stripe',
      name: 'Stripe',
      icon: CreditCard,
      description: 'Accept credit cards, bank transfers, and digital wallets',
      supported: ['USD', 'EUR', 'GBP', 'CAD', '40+ currencies'],
      fees: '2.9% + 30¢',
      recommended: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      icon: Globe,
      description: 'Global payment platform with buyer protection',
      supported: ['200+ countries', 'Multiple currencies', 'PayPal Credit'],
      fees: '2.9% + fixed fee',
      popular: true
    },
    {
      id: 'wise',
      name: 'Wise (formerly TransferWise)',
      icon: Building,
      description: 'Low-cost international payments and multi-currency accounts',
      supported: ['50+ currencies', 'Bank transfers', 'Wise card'],
      fees: '0.5-2% + fixed fee'
    }
  ];

  const selectedMethod = data.paymentMethod;

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Details Setup</h2>
        <p className="text-gray-600">Configure how you'll receive payments from buyers</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="w-5 h-5 mr-2 text-green-600" />
            Choose Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {paymentMethods.map((method) => {
              const Icon = method.icon;
              const isSelected = selectedMethod === method.id;
              
              return (
                <div
                  key={method.id}
                  className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                    isSelected 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleInputChange('paymentMethod', method.id)}
                >
                  {method.recommended && (
                    <Badge className="absolute -top-2 -right-2 bg-green-500 text-xs">
                      Recommended
                    </Badge>
                  )}
                  {method.popular && (
                    <Badge className="absolute -top-2 -right-2 bg-orange-500 text-xs">
                      Popular
                    </Badge>
                  )}
                  
                  <div className="flex items-center mb-3">
                    <Icon className="w-6 h-6 mr-2 text-blue-600" />
                    <h3 className="font-medium">{method.name}</h3>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{method.description}</p>
                  
                  <div className="space-y-2">
                    <div className="text-xs">
                      <span className="font-medium text-gray-700">Supported:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {method.supported.slice(0, 2).map((item, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {item}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-xs">
                      <span className="font-medium text-gray-700">Fees: </span>
                      <span className="text-green-600">{method.fees}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {selectedMethod && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="w-5 h-5 mr-2 text-blue-600" />
              {selectedMethod === 'stripe' ? 'Stripe' : selectedMethod === 'paypal' ? 'PayPal' : 'Wise'} Account Setup
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {selectedMethod === 'stripe' && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="businessName">Business/Legal Name *</Label>
                  <Input
                    id="businessName"
                    value={data.businessName || ''}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                    placeholder="Your business or legal name"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="taxId">Tax ID (EIN/SSN)</Label>
                    <Input
                      id="taxId"
                      value={data.taxId || ''}
                      onChange={(e) => handleInputChange('taxId', e.target.value)}
                      placeholder="***********"
                    />
                  </div>
                  <div>
                    <Label htmlFor="businessType">Business Type</Label>
                    <select
                      id="businessType"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={data.businessType || ''}
                      onChange={(e) => handleInputChange('businessType', e.target.value)}
                    >
                      <option value="">Select type</option>
                      <option value="individual">Individual</option>
                      <option value="company">Company</option>
                      <option value="llc">LLC</option>
                      <option value="corporation">Corporation</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {selectedMethod === 'paypal' && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="paypalEmail">PayPal Email Address *</Label>
                  <Input
                    id="paypalEmail"
                    type="email"
                    value={data.paypalEmail || ''}
                    onChange={(e) => handleInputChange('paypalEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="paypalAccountType">Account Type</Label>
                  <select
                    id="paypalAccountType"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={data.paypalAccountType || ''}
                    onChange={(e) => handleInputChange('paypalAccountType', e.target.value)}
                  >
                    <option value="">Select account type</option>
                    <option value="personal">Personal</option>
                    <option value="business">Business</option>
                  </select>
                </div>
              </div>
            )}

            {selectedMethod === 'wise' && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="wiseEmail">Wise Account Email *</Label>
                  <Input
                    id="wiseEmail"
                    type="email"
                    value={data.wiseEmail || ''}
                    onChange={(e) => handleInputChange('wiseEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="wiseCurrency">Primary Currency</Label>
                  <select
                    id="wiseCurrency"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={data.wiseCurrency || ''}
                    onChange={(e) => handleInputChange('wiseCurrency', e.target.value)}
                  >
                    <option value="">Select currency</option>
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                  </select>
                </div>
              </div>
            )}

            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
                <div>
                  <h4 className="font-medium text-yellow-900">Account Verification Required</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    You'll need to complete verification with {selectedMethod === 'stripe' ? 'Stripe' : selectedMethod === 'paypal' ? 'PayPal' : 'Wise'} 
                    before receiving payments. This process typically takes 1-2 business days.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Payout Schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="payoutSchedule">How often would you like to receive payments?</Label>
            <select
              id="payoutSchedule"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={data.payoutSchedule || ''}
              onChange={(e) => handleInputChange('payoutSchedule', e.target.value)}
            >
              <option value="">Select payout schedule</option>
              <option value="daily">Daily (available after 7 days)</option>
              <option value="weekly">Weekly (every Monday)</option>
              <option value="biweekly">Bi-weekly (1st and 15th)</option>
              <option value="monthly">Monthly (1st of each month)</option>
            </select>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="minimumPayout">Minimum Payout Amount</Label>
              <select
                id="minimumPayout"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={data.minimumPayout || ''}
                onChange={(e) => handleInputChange('minimumPayout', e.target.value)}
              >
                <option value="">Select minimum</option>
                <option value="25">$25</option>
                <option value="50">$50</option>
                <option value="100">$100</option>
                <option value="250">$250</option>
              </select>
            </div>
            <div>
              <Label htmlFor="currency">Payment Currency</Label>
              <select
                id="currency"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={data.currency || ''}
                onChange={(e) => handleInputChange('currency', e.target.value)}
              >
                <option value="">Select currency</option>
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="CAD">CAD - Canadian Dollar</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentDetailsSetup;
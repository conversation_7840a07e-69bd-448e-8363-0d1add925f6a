import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DollarSign } from "lucide-react";
import React, { useState, forwardRef, useImperativeHandle } from 'react';

interface PaymentSetupProps {
  formData: {
    paypalEmail: string;
  };
  onInputChange: (field: string, value: string) => void;
}

export const PaymentSetup = forwardRef(function PaymentSetup({ formData, onInputChange }: PaymentSetupProps, ref) {
  const [error, setError] = useState<string | null>(null);

  const validate = () => {
    if (!formData.paypalEmail) {
      setError('PayPal Email is required.');
      return false;
    }
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(formData.paypalEmail)) {
      setError('Enter a valid PayPal email address.');
      return false;
    }
    setError(null);
    return true;
  };

  useImperativeHandle(ref, () => ({ validate }));

  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-4">
          <DollarSign className="h-12 w-12 text-primary" />
        </div>
        <CardTitle>Payment Setup</CardTitle>
        <CardDescription>
          Where should we send your earnings?
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="paypalEmail">PayPal Email *</Label>
          <Input
            id="paypalEmail"
            type="email"
            placeholder="<EMAIL>"
            value={formData.paypalEmail}
            onChange={(e) => onInputChange('paypalEmail', e.target.value)}
          />
          {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          <p className="text-sm text-muted-foreground">
            We'll send your earnings to this PayPal account weekly
          </p>
        </div>
        
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Commission Rate</h4>
          <p className="text-sm text-blue-700">
            Platform fee: <strong>5%</strong> of each sale
            <br />
            You keep: <strong>95%</strong> of your earnings
          </p>
        </div>
      </CardContent>
    </Card>
  );
});

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Upload, Star, Plus, X, Award, Briefcase } from 'lucide-react';

interface ProfilePortfolioSetupProps {
  data: any;
  updateData: (data: any) => void;
}

const ProfilePortfolioSetup: React.FC<ProfilePortfolioSetupProps> = ({ data, updateData }) => {
  const [skills, setSkills] = useState<string[]>(data.skills || []);
  const [newSkill, setNewSkill] = useState('');

  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
  };

  const addSkill = () => {
    if (newSkill.trim() && !skills.includes(newSkill.trim())) {
      const updatedSkills = [...skills, newSkill.trim()];
      setSkills(updatedSkills);
      updateData({ skills: updatedSkills });
      setNewSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    const updatedSkills = skills.filter(skill => skill !== skillToRemove);
    setSkills(updatedSkills);
    updateData({ skills: updatedSkills });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Profile & Portfolio Setup</h2>
        <p className="text-gray-600">Showcase your skills and build an impressive profile</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="w-5 h-5 mr-2 text-yellow-500" />
              Professional Profile
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="displayName">Display Name *</Label>
              <Input
                id="displayName"
                value={data.displayName || ''}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                placeholder="How you want to appear to buyers"
              />
            </div>
            <div>
              <Label htmlFor="title">Professional Title *</Label>
              <Input
                id="title"
                value={data.title || ''}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., Full-Stack Developer, UI/UX Designer"
              />
            </div>
            <div>
              <Label htmlFor="experience">Years of Experience</Label>
              <select
                id="experience"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={data.experience || ''}
                onChange={(e) => handleInputChange('experience', e.target.value)}
              >
                <option value="">Select experience level</option>
                <option value="0-1">0-1 years</option>
                <option value="2-5">2-5 years</option>
                <option value="6-10">6-10 years</option>
                <option value="10+">10+ years</option>
              </select>
            </div>
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={data.location || ''}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="City, Country or 'Remote'"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2 text-purple-600" />
              Skills & Expertise
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Add Skills (up to 15)</Label>
              <div className="flex gap-2">
                <Input
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  placeholder="e.g., React, Figma, SEO"
                  onKeyPress={(e) => e.key === 'Enter' && addSkill()}
                />
                <Button onClick={addSkill} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {skills.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {skills.map((skill, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {skill}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-500"
                      onClick={() => removeSkill(skill)}
                    />
                  </Badge>
                ))}
              </div>
            )}

            <div>
              <Label>Suggested Skills</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['JavaScript', 'Python', 'React', 'Node.js', 'Figma', 'Photoshop', 'SEO', 'Content Writing'].map((skill) => (
                  <Badge
                    key={skill}
                    variant="outline"
                    className="cursor-pointer hover:bg-blue-50"
                    onClick={() => {
                      if (!skills.includes(skill)) {
                        const updatedSkills = [...skills, skill];
                        setSkills(updatedSkills);
                        updateData({ skills: updatedSkills });
                      }
                    }}
                  >
                    + {skill}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Briefcase className="w-5 h-5 mr-2 text-green-600" />
            Portfolio Samples
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((index) => (
              <div
                key={index}
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer"
              >
                <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">Upload Portfolio Item {index}</p>
                <p className="text-xs text-gray-500">Images, PDFs, or project links</p>
              </div>
            ))}
          </div>
          
          <div>
            <Label htmlFor="portfolioDescription">Portfolio Description</Label>
            <Textarea
              id="portfolioDescription"
              value={data.portfolioDescription || ''}
              onChange={(e) => handleInputChange('portfolioDescription', e.target.value)}
              placeholder="Describe your best work and achievements..."
              rows={3}
              className="mt-2"
            />
          </div>
        </CardContent>
      </Card>

      <div className="bg-purple-50 rounded-lg p-4">
        <div className="flex items-start">
          <i className="fas fa-lightbulb text-purple-600 mt-1 mr-3"></i>
          <div>
            <h4 className="font-medium text-purple-900">Portfolio Tips</h4>
            <p className="text-sm text-purple-700 mt-1">
              Include your best work that represents the services you'll offer. High-quality portfolio items help build buyer confidence.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePortfolioSetup;
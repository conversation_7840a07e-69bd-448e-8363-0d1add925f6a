import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Upload, Mail, User, Phone, Globe } from 'lucide-react';

interface SellerRegistrationProps {
  data: any;
  updateData: (data: any) => void;
}

const SellerRegistration: React.FC<SellerRegistrationProps> = ({ data, updateData }) => {
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Registration & Verification</h2>
        <p className="text-gray-600">Create your seller account and verify your identity</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2 text-blue-600" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="fullName">Full Name *</Label>
              <Input
                id="fullName"
                value={data.fullName || ''}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                placeholder="Enter your full name"
              />
            </div>
            <div>
              <Label htmlFor="email">Email Address *</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="email"
                  type="email"
                  className="pl-10"
                  value={data.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="phone"
                  className="pl-10"
                  value={data.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="website">Website/Portfolio URL</Label>
              <div className="relative">
                <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="website"
                  className="pl-10"
                  value={data.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://yourportfolio.com"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 mr-2 text-green-600" />
              Identity Verification
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Profile Photo</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer">
                <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
              </div>
            </div>
            <div>
              <Label>Government ID Document</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors cursor-pointer">
                <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">Upload ID for verification</p>
                <p className="text-xs text-gray-500">Driver's License, Passport, or ID Card</p>
              </div>
              <Badge variant="outline" className="mt-2">
                <i className="fas fa-shield-alt mr-1"></i>
                Your documents are encrypted and secure
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Professional Bio</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="bio">Tell us about yourself and your expertise</Label>
            <Textarea
              id="bio"
              value={data.bio || ''}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              placeholder="Describe your skills, experience, and what makes you unique as a seller..."
              rows={4}
              className="mt-2"
            />
            <p className="text-sm text-gray-500 mt-1">
              This will be displayed on your seller profile ({data.bio?.length || 0}/500 characters)
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="flex items-start">
          <i className="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
          <div>
            <h4 className="font-medium text-blue-900">Verification Process</h4>
            <p className="text-sm text-blue-700 mt-1">
              Your documents will be reviewed within 24-48 hours. You'll receive an email once verification is complete.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerRegistration;
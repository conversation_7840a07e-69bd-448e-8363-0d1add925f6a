import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';

export interface Notification {
  id: number;
  title: string;
  description: string;
  messageId?: string;
  read?: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: number) => void;
  clearNotificationsForUser: (userId: string) => void;
  onNotificationClick?: (notification: Notification) => void;
  setOnNotificationClick: (callback: (notification: Notification) => void) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const ctx = useContext(NotificationContext);
  if (!ctx) throw new Error('useNotification must be used within NotificationProvider');
  return ctx;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [onNotificationClick, setOnNotificationClick] = useState<((notification: Notification) => void) | undefined>();

  useEffect(() => {
    if (!user) return;
    const channel = supabase.channel('realtime-messages-global')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
      }, (payload) => {
        const msg = payload.new;
        // Only notify if the current user is the recipient (not sender)
        if (msg.recipient_id === user.id) {
          setNotifications((prev) => [
            {
              id: Date.now(),
              title: `New message from ${msg.sender_name}`,
              description: msg.subject || msg.content?.substring(0, 50) || 'New message',
              messageId: msg.id,
              read: false,
            },
            ...prev
          ]);
        }
      })
      .subscribe();

      console.log('notifications', channel);
    return () => { supabase.removeChannel(channel); };
  }, [user]);

  const markAsRead = (id: number) => {
    setNotifications((prev) => prev.map(n => n.id === id ? { ...n, read: true } : n));
  };

  const clearNotificationsForUser = (userId: string) => {
    setNotifications((prev) => prev.filter(n => !n.title.includes(userId)));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <NotificationContext.Provider value={{ 
      notifications, 
      unreadCount, 
      markAsRead, 
      clearNotificationsForUser,
      onNotificationClick,
      setOnNotificationClick 
    }}>
      {children}
    </NotificationContext.Provider>
  );
}; 
import React, { createContext, useContext, ReactNode } from 'react';
import { useSiteSettings } from '@/hooks/useSupabaseData';

interface SiteSettingsContextType {
  settings: Record<string, string> | undefined;
  isLoading: boolean;
  getSetting: (key: string, defaultValue?: string) => string;
}

const SiteSettingsContext = createContext<SiteSettingsContextType | undefined>(undefined);

export const useSiteSettingsContext = () => {
  const context = useContext(SiteSettingsContext);
  if (context === undefined) {
    throw new Error('useSiteSettingsContext must be used within a SiteSettingsProvider');
  }
  return context;
};

interface SiteSettingsProviderProps {
  children: ReactNode;
}

export const SiteSettingsProvider: React.FC<SiteSettingsProviderProps> = ({ children }) => {
  const { data: settings, isLoading } = useSiteSettings();

  const getSetting = (key: string, defaultValue: string = '') => {
    return settings?.[key] || defaultValue;
  };

  const value: SiteSettingsContextType = {
    settings,
    isLoading,
    getSetting,
  };

  return (
    <SiteSettingsContext.Provider value={value}>
      {children}
    </SiteSettingsContext.Provider>
  );
}; 
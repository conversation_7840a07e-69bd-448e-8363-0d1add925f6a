import { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useListing, useListingReviews, useAddToWishlist, useRemoveFromWishlist, useIsInWishlist } from '@/hooks/useSupabaseData';
import { StripeClient } from '@/integrations/stripe/client';

// Profile hook for seller information
const useProfile = (id?: string) => {
  return useQuery({
    queryKey: ['profile', id],
    queryFn: async () => {
      if (!id) return null;
      const { data, error } = await supabase
        .from('profiles')
        .select('*, listings(*), reviews:reviews(*, profiles(*))')
        .eq('id', id)
        .single();
      if (error) throw error;
      return data;
    },
    enabled: !!id
  });
};

export const useProductDetail = () => {
  const { identifier } = useParams<{ identifier: string }>();
  const { user } = useAuth();
  const { toast } = useToast();

  // Data fetching
  const { data: product, isLoading } = useListing(identifier || '');
  const { data: reviews, isLoading: reviewsLoading } = useListingReviews(product?.id || '');
  const { data: seller } = useProfile(product?.seller_id);
  
  // Wishlist functionality
  const addToWishlist = useAddToWishlist();
  const removeFromWishlist = useRemoveFromWishlist();
  const { data: isInWishlist } = useIsInWishlist(user?.id, product?.id);

  // State
  const [buyNowLoading, setBuyNowLoading] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  // Computed values
  const sellerId = product?.seller_id;
  const sellerReviews = seller?.reviews || [];
  const totalSales = sellerReviews.length;
  const avgRating = reviews && reviews.length > 0 
    ? reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.length 
    : 0;

  // Check ownership and purchase status
  const isProductOwner = user?.id === sellerId;
  const hasPurchased = useMemo(() => {
    // TODO: Check if user has purchased this product
    return false;
  }, [user?.id, product?.id]);

  // Purchase functionality
  const handleStripeCheckout = async () => {
    if (!user || !product) {
      toast({
        title: "Login Required",
        description: "Please log in to make a purchase.",
      });
      return;
    }

    setBuyNowLoading(true);
    try {
      const baseUrl = window.location.origin;
      await StripeClient.initiatePurchase({
        price: product.price || 0,
        productName: product.title,
        productId: product.id,
        buyerEmail: user.email || '',
        successUrl: `${baseUrl}/order-confirmation?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${baseUrl}/order-cancelled?product_id=${product.id}`,
      });
    } catch (error) {
      console.error('Stripe checkout error:', error);
      toast({
        title: "Checkout Error",
        description: "Failed to initiate checkout. Please try again.",
      });
    } finally {
      setBuyNowLoading(false);
    }
  };

  // Wishlist functionality
  const handleWishlist = async () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to add items to your wishlist.",
      });
      return;
    }

    if (!product) return;

    try {
      if (isInWishlist) {
        await removeFromWishlist.mutateAsync({ userId: user.id, listingId: product.id });
        toast({
          title: "Removed from Wishlist",
          description: `This ${product.type} has been removed from your wishlist.`,
        });
      } else {
        await addToWishlist.mutateAsync({ userId: user.id, listingId: product.id });
        toast({
          title: "Added to Wishlist",
          description: `This ${product.type} has been added to your wishlist.`,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
      });
    }
  };

  // Preview functionality
  const handlePreview = () => {
    setShowPreviewModal(true);
  };

  // Share functionality
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.title || 'Product',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link Copied",
        description: "Product link copied to clipboard.",
      });
    }
  };

  // Message functionality
  const handleMessageSent = () => {
    toast({
      title: "Message Sent",
      description: "Your message has been sent to the seller.",
    });
  };

  return {
    // Data
    product,
    reviews: reviews || [],
    seller,
    isLoading,
    reviewsLoading,
    
    // Computed values
    avgRating,
    totalSales,
    isProductOwner,
    hasPurchased,
    isInWishlist: !!isInWishlist,
    
    // State
    buyNowLoading,
    showPreviewModal,
    setShowPreviewModal,
    
    // Actions
    handleStripeCheckout,
    handleWishlist,
    handlePreview,
    handleShare,
    handleMessageSent,
  };
};

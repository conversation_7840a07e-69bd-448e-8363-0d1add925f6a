import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { Tables } from '@/integrations/supabase/types';

// Hook for fetching categories
export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data as Tables<'categories'>[];
    },
  });
};

// Hook for fetching listings with optional filters
export const useListings = (options?: {
  featured?: boolean;
  type?: string;
  categoryId?: string;
  status?: string;
  limit?: number;
  orderBy?: 'created_at' | 'rating' | 'price';
  orderDirection?: 'asc' | 'desc';
}) => {
  return useQuery({
    queryKey: ['listings', options],
    queryFn: async () => {
      let query = supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `);

      // Only show active listings by default
      if (!options?.status) {
        query = query.eq('status', 'active');
      } else {
        query = query.eq('status', options.status);
      }

      // Apply filters
      if (options?.featured !== undefined) {
        query = query.eq('featured', options.featured);
      }

      if (options?.type) {
        query = query.eq('type', options.type);
      }

      if (options?.categoryId) {
        query = query.eq('category_id', options.categoryId);
      }

      // Apply ordering
      if (options?.orderBy) {
        query = query.order(options.orderBy, {
          ascending: options.orderDirection === 'asc'
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply limit
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching a single listing by ID or slug
export const useListing = (identifier: string) => {
  return useQuery({
    queryKey: ['listing', identifier],
    queryFn: async () => {
      // Check if identifier is a UUID (contains hyphens and is 36 chars)
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);

      let query = supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `);

      if (isUUID) {
        query = query.eq('id', identifier);
      } else {
        query = query.eq('slug', identifier);
      }

      const { data, error } = await query.single();

      if (error) throw error;
      return data;
    },
    enabled: !!identifier,
  });
};

// Hook for fetching top rated listings
export const useTopRatedListings = (limit: number = 6) => {
  return useQuery({
    queryKey: ['listings', 'top-rated', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'active')
        .order('rating', { ascending: false })
        .order('review_count', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching trending listings (featured + high rating)
export const useTrendingListings = (limit: number = 6) => {
  return useQuery({
    queryKey: ['listings', 'trending', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'active')
        .eq('featured', true)
        .order('rating', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching recent listings
export const useRecentListings = (limit: number = 6) => {
  return useQuery({
    queryKey: ['listings', 'recent', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching listings by category
export const useListingsByCategory = (categorySlug: string, limit?: number) => {
  return useQuery({
    queryKey: ['listings', 'category', categorySlug, limit],
    queryFn: async () => {
      let query = supabase
        .from('listings')
        .select(`
          *,
          categories!inner (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'active')
        .eq('categories.slug', categorySlug)
        .order('rating', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data;
    },
    enabled: !!categorySlug,
  });
};

// Hook for fetching listings by type
export const useListingsByType = (type: string, limit?: number) => {
  return useQuery({
    queryKey: ['listings', 'type', type, limit],
    queryFn: async () => {
      let query = supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'active')
        .eq('type', type)
        .order('rating', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data;
    },
    enabled: !!type,
  });
};

// Hook for fetching user orders
export const useUserOrders = (userId?: string) => {
  console.log(">>>>>>>>>>>>>>> userId >>>>>>>>>>>", userId)
  return useQuery({
    queryKey: ['orders', 'user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          listings (
            id,
            title,
            image_url
          ),
          profiles!orders_seller_id_fkey (
            id,
            full_name
          )
        `)
        .eq('buyer_id', userId!)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
  });
};

// Hook for fetching seller orders
export const useSellerOrders = (sellerId?: string) => {
  return useQuery({
    queryKey: ['orders', 'seller', sellerId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          listings (
            id,
            title,
            image_url
          ),
          profiles!orders_buyer_id_fkey (
            id,
            full_name
          )
        `)
        .eq('seller_id', sellerId!)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!sellerId,
  });
};

// Hook for fetching user downloads
export const useUserDownloads = (userId?: string) => {
  return useQuery({
    queryKey: ['downloads', 'user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('downloads')
        .select('*')
        .eq('user_name', userId!)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
  });
};

// Hook for fetching user messages
export const useUserMessages = (userId?: string) => {
  return useQuery({
    queryKey: ['messages', 'user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .or(`sender_name.eq.${userId},recipient_name.eq.${userId}`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
  });
};

// Hook for fetching reviews for a listing
export const useListingReviews = (listingId: string) => {
  return useQuery({
    queryKey: ['reviews', 'listing', listingId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('listing_id', listingId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!listingId,
  });
};

// Hook for fetching all users (admin only)
export const useAllUsers = () => {
  return useQuery({
    queryKey: ['users', 'all'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching all orders (admin only)
export const useAllOrders = (limit?: number) => {
  return useQuery({
    queryKey: ['orders', 'all', limit],
    queryFn: async () => {
      let query = supabase
        .from('orders')
        .select(`
          *,
          listings (
            id,
            title,
            image_url
          ),
          profiles!orders_buyer_id_fkey (
            id,
            full_name
          ),
          profiles!orders_seller_id_fkey (
            id,
            full_name
          )
        `)
        .order('created_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching all listings (admin only)
export const useAllListings = (options?: {
  status?: string;
  limit?: number;
  orderBy?: 'created_at' | 'rating' | 'price';
  orderDirection?: 'asc' | 'desc';
}) => {
  return useQuery({
    queryKey: ['listings', 'all', options],
    queryFn: async () => {
      let query = supabase
        .from('listings')
        .select(`
          *,
          categories (
            id,
            name,
            slug
          ),
          profiles (
            id,
            full_name
          )
        `);

      // Apply status filter if provided
      if (options?.status) {
        query = query.eq('status', options.status);
      }

      // Apply ordering
      if (options?.orderBy) {
        query = query.order(options.orderBy, {
          ascending: options.orderDirection === 'asc'
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply limit
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });
};

// Hook for fetching commission and revenue stats (admin only)
export const useCommissionStats = () => {
  return useQuery({
    queryKey: ['stats', 'commission'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select('amount, commission, created_at')
        .eq('status', 'completed');

      if (error) throw error;

      const totalRevenue = data?.reduce((sum, order) => sum + (order.amount || 0), 0) || 0;
      const totalCommission = data?.reduce((sum, order) => sum + (order.commission || 0), 0) || 0;
      const totalOrders = data?.length || 0;

      // Calculate monthly stats
      const now = new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const monthlyData = data?.filter(order => 
        new Date(order.created_at) >= lastMonth
      ) || [];

      const monthlyRevenue = monthlyData.reduce((sum, order) => sum + (order.amount || 0), 0);
      const monthlyCommission = monthlyData.reduce((sum, order) => sum + (order.commission || 0), 0);

      return {
        totalRevenue,
        totalCommission,
        totalOrders,
        monthlyRevenue,
        monthlyCommission,
        monthlyOrders: monthlyData.length
      };
    },
  });
};

// Wishlist hooks
export const useWishlist = (userId?: string) => {
  return useQuery({
    queryKey: ['wishlist', userId],
    queryFn: async () => {
      if (!userId) return [];
      const { data, error } = await supabase
        .from('wishlist')
        .select(`
          *,
          listings (
            *,
            categories (name),
            profiles (full_name)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      if (error) throw error;
      return data || [];
    },
    enabled: !!userId
  });
};

export const useAddToWishlist = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, listingId }: { userId: string; listingId: string }) => {
      const { data, error } = await supabase
        .from('wishlist')
        .insert([{ user_id: userId, listing_id: listingId }]);
      if (error) throw error;
      return data;
    },
    onMutate: async ({ userId, listingId }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['wishlist', userId] });
      await queryClient.cancelQueries({ queryKey: ['isInWishlist', userId, listingId] });

      // Snapshot the previous value
      const previousWishlist = queryClient.getQueryData(['wishlist', userId]);
      const previousIsInWishlist = queryClient.getQueryData(['isInWishlist', userId, listingId]);

      // Optimistically update wishlist
      queryClient.setQueryData(['wishlist', userId], (old: any) => {
        if (!old) return old;
        return [...old, { id: `temp-${Date.now()}`, user_id: userId, listing_id: listingId, created_at: new Date().toISOString() }];
      });

      // Optimistically update isInWishlist
      queryClient.setQueryData(['isInWishlist', userId, listingId], true);

      return { previousWishlist, previousIsInWishlist };
    },
    onError: (err, { userId, listingId }, context) => {
      // Rollback on error
      if (context?.previousWishlist) {
        queryClient.setQueryData(['wishlist', userId], context.previousWishlist);
      }
      if (context?.previousIsInWishlist !== undefined) {
        queryClient.setQueryData(['isInWishlist', userId, listingId], context.previousIsInWishlist);
      }
    },
    onSettled: (data, error, { userId, listingId }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
      queryClient.invalidateQueries({ queryKey: ['isInWishlist', userId, listingId] });
    },
  });
};

export const useRemoveFromWishlist = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, listingId }: { userId: string; listingId: string }) => {
      const { data, error } = await supabase
        .from('wishlist')
        .delete()
        .eq('user_id', userId)
        .eq('listing_id', listingId);
      if (error) throw error;
      return data;
    },
    onMutate: async ({ userId, listingId }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['wishlist', userId] });
      await queryClient.cancelQueries({ queryKey: ['isInWishlist', userId, listingId] });

      // Snapshot the previous value
      const previousWishlist = queryClient.getQueryData(['wishlist', userId]);
      const previousIsInWishlist = queryClient.getQueryData(['isInWishlist', userId, listingId]);

      // Optimistically update wishlist
      queryClient.setQueryData(['wishlist', userId], (old: any) => {
        if (!old) return old;
        return old.filter((item: any) => item.listing_id !== listingId);
      });

      // Optimistically update isInWishlist
      queryClient.setQueryData(['isInWishlist', userId, listingId], false);

      return { previousWishlist, previousIsInWishlist };
    },
    onError: (err, { userId, listingId }, context) => {
      // Rollback on error
      if (context?.previousWishlist) {
        queryClient.setQueryData(['wishlist', userId], context.previousWishlist);
      }
      if (context?.previousIsInWishlist !== undefined) {
        queryClient.setQueryData(['isInWishlist', userId, listingId], context.previousIsInWishlist);
      }
    },
    onSettled: (data, error, { userId, listingId }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
      queryClient.invalidateQueries({ queryKey: ['isInWishlist', userId, listingId] });
    },
  });
};

export const useIsInWishlist = (userId?: string, listingId?: string) => {
  return useQuery({
    queryKey: ['isInWishlist', userId, listingId],
    queryFn: async () => {
      if (!userId || !listingId) return false;
      const { data, error } = await supabase
        .from('wishlist')
        .select('id')
        .eq('user_id', userId)
        .eq('listing_id', listingId)
        .single();
      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
      return !!data;
    },
    enabled: !!userId && !!listingId
  });
};

// Site Settings hooks
export const useSiteSettings = () => {
  return useQuery({
    queryKey: ['site-settings'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .order('key');
      if (error) throw error;
      
      // Convert array to object for easier access
      const settings: Record<string, string> = {};
      data?.forEach(setting => {
        settings[setting.key] = setting.value;
      });
      return settings;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateSiteSetting = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ key, value }: { key: string; value: string }) => {
      const { data, error } = await supabase
        .from('site_settings')
        .upsert({ key, value, updated_at: new Date().toISOString() })
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['site-settings'] });
    },
  });
};

export const useSiteSetting = (key: string) => {
  const { data: settings } = useSiteSettings();
  return settings?.[key] || '';
};

// src/integrations/stripe/client.ts
import { supabase } from '../supabase/client';

export interface StripeCheckoutParams {
  price: number;
  productName: string;
  productId: string;
  buyerEmail: string;
  successUrl: string;
  cancelUrl: string;
  buyerId: string;
}

export interface StripeCheckoutResponse {
  url: string;
  id: string;
}

export class StripeClient {
  private static async callEdgeFunction(
    functionName: string,
    params: any
  ): Promise<any> {
    const { data, error } = await supabase.functions.invoke(functionName, {
      body: params,
    });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  static async createCheckoutSession(params: StripeCheckoutParams): Promise<StripeCheckoutResponse> {
    try {
      console.log(">>>>>>>>>>>> intial call")
      const response = await this.callEdgeFunction('stripeCheckout', params);
      console.log(">>>>>>>>>>>> after intial call")
      if (response.error) {
        throw new Error(response.error);
      }

      return {
        url: response.url,
        id: response.id,
      };
    } catch (error) {
      console.error('Stripe checkout error:', error);
      throw error;
    }
  }

  static redirectToCheckout(checkoutUrl: string): void {
    window.location.href = checkoutUrl;
  }

  static async initiatePurchase(params: StripeCheckoutParams): Promise<void> {
    try {
      const checkoutSession = await this.createCheckoutSession(params);
      this.redirectToCheckout(checkoutSession.url);
    } catch (error) {
      console.error('Failed to initiate purchase:', error);
      throw error;
    }
  }
} 
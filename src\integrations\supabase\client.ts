// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jecfmpvnvpnblzkxqjys.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImplY2ZtcHZudnBuYmx6a3hxanlzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MTIzMTAsImV4cCI6MjA2Nzk4ODMxMH0.FSt7LAt4H6RBAJXepBy68vnXNoEMiWBQQrEUMX8Si2Q";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import DashboardSection from '@/components/admin/sections/DashboardSection';
import UserManagementSection from '@/components/admin/sections/UserManagementSection';
import ContentManagementSection from '@/components/admin/sections/ContentManagementSection';
import SiteSettingsSection from '@/components/admin/sections/SiteSettingsSection';
import { CommissionSection } from '@/components/admin/sections/CommissionSection';
import { useAuth } from '@/contexts/AuthContext';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend
} from 'recharts';

export default function Admin() {
  const { user, profile, loading } = useAuth();
  if (loading) {
    return <div>Loading...</div>;
  }
  // Check if user is authenticated and is an admin
  const isAdmin = user?.user_metadata?.is_admin === true || profile?.is_admin === true;
  if (!user || !isAdmin) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <AdminSidebar />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <Routes>
            <Route path="/" element={<DashboardSection />} />
            <Route path="/users" element={<UserManagementSection />} />
            <Route path="/content" element={<ContentManagementSection />} />
            <Route path="/settings" element={<SiteSettingsSection />} />
            <Route path="/financial" element={<CommissionSection />} />
            <Route path="/analytics" element={<AnalyticsSection />} />
            <Route path="/reports" element={<ReportsSection />} />
            <Route path="/security" element={<SecuritySection />} />
            <Route path="/system" element={<SystemSection />} />
            <Route path="/activity" element={<ActivitySection />} />
          </Routes>
        </div>
      </div>
    </div>
  );
}

// Placeholder sections for future implementation
function AnalyticsSection() {
  // Dummy data
  const stats = [
    { label: 'Total Users', value: 1245 },
    { label: 'Total Sellers', value: 312 },
    { label: 'Total Listings', value: 876 },
  ];
  const revenueData = [
    { name: 'Total Revenue', value: 42500 },
    { name: 'Commission Earned', value: 4250 },
  ];
  const COLORS = ['#8884d8', '#82ca9d'];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Analytics</h1>
      {/* Dashboard Overview Bar Chart */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-2">Platform Overview</h2>
        <ResponsiveContainer width="100%" height={220}>
          <BarChart data={stats} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="label" />
            <YAxis allowDecimals={false} />
            <Tooltip />
            <Bar dataKey="value" fill="#8884d8" radius={[8, 8, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
      {/* Revenue & Commission Pie Chart */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-2">Revenue Breakdown</h2>
        <ResponsiveContainer width="100%" height={220}>
          <PieChart>
            <Pie
              data={revenueData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={70}
              label
            >
              {revenueData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

function ReportsSection() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Reports</h1>
      <p>Reports section coming soon...</p>
    </div>
  );
}

function SecuritySection() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Security</h1>
      <p>Security settings coming soon...</p>
    </div>
  );
}

function SystemSection() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">System</h1>
      <p>System settings coming soon...</p>
    </div>
  );
}

function ActivitySection() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Activity Log</h1>
      <p>Activity log coming soon...</p>
    </div>
  );
}

import { ArrowLeft, Search, Shield, Star, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { PageLayout } from "@/components/layout/PageLayout";

export default function BuyerGuide() {
  const steps = [
    {
      title: "Browse & Search",
      description: "Find the perfect service or product for your needs",
      icon: Search,
      details: [
        "Use filters to narrow down results",
        "Read seller profiles and reviews",
        "Compare different options"
      ]
    },
    {
      title: "Choose Quality Sellers",
      description: "Select sellers with great reviews and portfolios",
      icon: Star,
      details: [
        "Check seller ratings and feedback",
        "Review portfolio samples",
        "Look for verified sellers"
      ]
    },
    {
      title: "Secure Payment",
      description: "Pay safely through our secure platform",
      icon: CreditCard,
      details: [
        "Multiple payment options",
        "Escrow protection",
        "Refund guarantee"
      ]
    },
    {
      title: "Stay Protected",
      description: "Our buyer protection keeps you safe",
      icon: Shield,
      details: [
        "24/7 customer support",
        "Dispute resolution",
        "Money-back guarantee"
      ]
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Link to="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-bold">Buyer Guide</h1>
          <p className="text-xl text-muted-foreground mt-2">
            Your complete guide to buying on EpicFreelancer
          </p>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {steps.map((step, index) => (
              <Card key={step.title} className="relative">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-lg font-bold">
                    {index + 1}
                  </div>
                  <step.icon className="mx-auto h-8 w-8 text-primary mb-2" />
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                  <CardDescription>{step.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-1">
                    {step.details.map((detail) => (
                      <li key={detail} className="text-sm text-muted-foreground flex items-center gap-2">
                        <div className="w-1 h-1 bg-primary rounded-full" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle>Tips for Success</CardTitle>
              <CardDescription>Make the most of your EpicFreelancer experience</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                <div>
                  <h4 className="font-medium">Communicate clearly</h4>
                  <p className="text-sm text-muted-foreground">Provide detailed requirements to get the best results</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                <div>
                  <h4 className="font-medium">Set realistic timelines</h4>
                  <p className="text-sm text-muted-foreground">Allow sufficient time for quality work</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                <div>
                  <h4 className="font-medium">Leave honest reviews</h4>
                  <p className="text-sm text-muted-foreground">Help other buyers and support good sellers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Safety & Security</CardTitle>
              <CardDescription>How we keep your purchases safe</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h4 className="font-medium">Secure payments</h4>
                  <p className="text-sm text-muted-foreground">All transactions are encrypted and protected</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h4 className="font-medium">Verified sellers</h4>
                  <p className="text-sm text-muted-foreground">We verify seller identities and qualifications</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h4 className="font-medium">Dispute resolution</h4>
                  <p className="text-sm text-muted-foreground">Fair resolution process for any issues</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Start Buying?</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Explore thousands of services and products from talented professionals worldwide.
          </p>
          <div className="flex gap-4 justify-center">
            <Link to="/">
              <Button size="lg">Browse Services</Button>
            </Link>
            <Link to="/contact">
              <Button variant="outline" size="lg">Contact Support</Button>
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
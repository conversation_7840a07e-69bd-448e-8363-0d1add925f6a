import { ArrowLeft, <PERSON>, Palette, Zap, Briefcase, Camera } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";
import { PageLayout } from "@/components/layout/PageLayout";

export default function Categories() {
  const categories = [
    {
      title: "Digital Products",
      description: "Templates, courses, ebooks, and digital downloads",
      icon: Zap,
      count: "2,500+ products",
      examples: ["Website templates", "Online courses", "Stock photos", "Digital art"]
    },
    {
      title: "Freelance Services",
      description: "Professional services from talented freelancers",
      icon: Briefcase,
      count: "10,000+ services",
      examples: ["Content writing", "Virtual assistance", "Consulting", "Project management"]
    },
    {
      title: "Micro SaaS Tools",
      description: "Small software solutions for specific needs",
      icon: Code,
      count: "500+ tools",
      examples: ["Analytics tools", "Automation scripts", "API services", "Chrome extensions"]
    },
    {
      title: "Design & Creative",
      description: "Visual design, branding, and creative services",
      icon: Palette,
      count: "5,000+ services",
      examples: ["Logo design", "UI/UX design", "Illustrations", "Brand identity"]
    },
    {
      title: "Development & Tech",
      description: "Software development and technical services",
      icon: Code,
      count: "3,000+ services",
      examples: ["Web development", "Mobile apps", "Database design", "DevOps"]
    },
    {
      title: "Photography & Video",
      description: "Visual content creation and editing",
      icon: Camera,
      count: "1,500+ services",
      examples: ["Photo editing", "Video production", "Animation", "Product photography"]
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Link to="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-bold">Browse Categories</h1>
          <p className="text-xl text-muted-foreground mt-2">
            Explore all categories and find exactly what you need
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category.title} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <category.icon className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                    <div className="text-sm text-primary font-medium">{category.count}</div>
                  </div>
                </div>
                <CardDescription className="mt-2">{category.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">Popular services:</h4>
                  <div className="flex flex-wrap gap-1">
                    {category.examples.map((example) => (
                      <span 
                        key={example} 
                        className="text-xs bg-muted px-2 py-1 rounded-md"
                      >
                        {example}
                      </span>
                    ))}
                  </div>
                </div>
                <Button className="w-full mt-4" variant="outline">
                  Browse {category.title}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Can't Find What You're Looking For?</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Post a custom request and let sellers come to you with their proposals.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg">Post a Request</Button>
            <Link to="/contact">
              <Button variant="outline" size="lg">Contact Support</Button>
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PageLayout } from "@/components/layout/PageLayout";

const Design = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    {
      title: "Logo Design",
      description: "Professional logos and brand identity packages",
      count: "2,400+ designs"
    },
    {
      title: "UI/UX Design",
      description: "User interface and experience design services",
      count: "1,800+ designs"
    },
    {
      title: "Graphic Design",
      description: "Print design, banners, and marketing materials",
      count: "3,200+ designs"
    },
    {
      title: "Web Design",
      description: "Website mockups and design templates",
      count: "1,500+ designs"
    },
    {
      title: "Illustration",
      description: "Custom illustrations and digital artwork",
      count: "950+ designs"
    },
    {
      title: "Branding",
      description: "Complete brand identity and style guides",
      count: "720+ designs"
    }
  ];

  const featuredDesigns = [
    {
      title: "Professional Logo Design Package",
      startingPrice: "$99",
      rating: 4.9,
      reviews: 234,
      category: "Logo Design",
      delivery: "3 days"
    },
    {
      title: "Modern UI/UX Design System",
      startingPrice: "$299",
      rating: 4.8,
      reviews: 156,
      category: "UI/UX Design",
      delivery: "7 days"
    },
    {
      title: "Brand Identity Package",
      startingPrice: "$199",
      rating: 4.7,
      reviews: 89,
      category: "Branding",
      delivery: "5 days"
    },
    {
      title: "Custom Illustration Set",
      startingPrice: "$149",
      rating: 4.9,
      reviews: 67,
      category: "Illustration",
      delivery: "4 days"
    }
  ];

  return (
    <PageLayout>
      <section className="py-16 px-4 bg-gradient-primary text-white">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Design Services
          </h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Professional design services from talented creators worldwide
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search design services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-32 py-4 text-lg rounded-full border-none bg-white text-gray-900"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
              <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 whitespace-nowrap">
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 py-16">
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Design Categories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary">{category.count}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Designs</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredDesigns.map((design, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
                  <CardTitle className="text-lg line-clamp-2">{design.title}</CardTitle>
                  <CardDescription>
                    <Badge variant="outline" className="mb-2">{design.category}</Badge>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Starting at</span>
                      <span className="text-2xl font-bold text-primary">{design.startingPrice}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        {design.rating} ({design.reviews})
                      </div>
                      <span>{design.delivery}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Design;

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PageLayout } from "@/components/layout/PageLayout";

const Development = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    {
      title: "Web Development",
      description: "Custom websites, web apps, and e-commerce solutions",
      count: "1,800+ services"
    },
    {
      title: "Mobile Development",
      description: "iOS and Android app development services",
      count: "1,200+ services"
    },
    {
      title: "Backend Development",
      description: "APIs, databases, and server-side development",
      count: "950+ services"
    },
    {
      title: "Frontend Development",
      description: "React, Vue, Angular, and modern frontend frameworks",
      count: "1,400+ services"
    },
    {
      title: "Full-Stack Development",
      description: "Complete end-to-end application development",
      count: "800+ services"
    },
    {
      title: "DevOps & Cloud",
      description: "Deployment, CI/CD, and cloud infrastructure setup",
      count: "600+ services"
    }
  ];

  const featuredServices = [
    {
      title: "Custom React Web Application",
      startingPrice: "$799",
      rating: 4.9,
      reviews: 156,
      category: "Web Development",
      delivery: "14 days"
    },
    {
      title: "Mobile App Development (iOS/Android)",
      startingPrice: "$1,299",
      rating: 4.8,
      reviews: 89,
      category: "Mobile Development",
      delivery: "21 days"
    },
    {
      title: "REST API Development",
      startingPrice: "$399",
      rating: 4.7,
      reviews: 234,
      category: "Backend Development",
      delivery: "10 days"
    },
    {
      title: "Full-Stack E-commerce Platform",
      startingPrice: "$1,999",
      rating: 4.9,
      reviews: 67,
      category: "Full-Stack Development",
      delivery: "30 days"
    }
  ];

  return (
    <PageLayout>
      <section className="py-16 px-4 bg-gradient-primary text-white">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Development Services
          </h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Expert developers ready to build your next project
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search development services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-32 py-4 text-lg rounded-full border-none bg-white text-gray-900"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
              <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 whitespace-nowrap">
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 py-16">
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Development Categories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary">{category.count}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Development Services</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredServices.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
                  <CardTitle className="text-lg line-clamp-2">{service.title}</CardTitle>
                  <CardDescription>
                    <Badge variant="outline" className="mb-2">{service.category}</Badge>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Starting at</span>
                      <span className="text-2xl font-bold text-primary">{service.startingPrice}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        {service.rating} ({service.reviews})
                      </div>
                      <span>{service.delivery}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Development;

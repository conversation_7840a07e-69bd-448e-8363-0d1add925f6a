import React, { useState } from 'react';
import { Header } from '@/components/homepage/Header';
import { HeroSection } from '@/components/homepage/HeroSection';
import { TrendingSection } from '@/components/homepage/TrendingSection';
import { RecentListingsSection } from '@/components/homepage/RecentListingsSection';
import { TopRatedSection } from '@/components/homepage/TopRatedSection';
import { DigitalProductsSection } from '@/components/homepage/DigitalProductsSection';
import { ServicesSection } from '@/components/homepage/ServicesSection';
import { MicroSaasSection } from '@/components/homepage/MicroSaasSection';
import { SellerCtaSection } from '@/components/homepage/SellerCtaSection';
import { Footer } from '@/components/homepage/Footer';

const EpicFreelancer = () => {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-gray-50">
      <Header searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      <HeroSection />
      <TrendingSection />
      <RecentListingsSection />
      <TopRatedSection />
      <DigitalProductsSection />
      <ServicesSection />
      <MicroSaasSection />
      <SellerCtaSection />
      <Footer />
    </div>
  );
};

export default EpicFreelancer;
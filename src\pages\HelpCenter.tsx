import { ArrowLeft, MessageCircle, Book, Users, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { PageLayout } from "@/components/layout/PageLayout";

export default function HelpCenter() {
  const helpCategories = [
    {
      title: "Getting Started",
      description: "Learn the basics of using EpicFreelancer",
      icon: Book,
      articles: ["How to create an account", "Setting up your profile", "Making your first purchase"]
    },
    {
      title: "For Sellers",
      description: "Guides for selling on our platform",
      icon: Users,
      articles: ["Creating your first listing", "Managing orders", "Payment processing"]
    },
    {
      title: "For Buyers",
      description: "Everything you need to know as a buyer",
      icon: MessageCircle,
      articles: ["How to place an order", "Working with sellers", "Dispute resolution"]
    },
    {
      title: "Technical Support",
      description: "Get help with technical issues",
      icon: Phone,
      articles: ["Account issues", "Payment problems", "Site navigation"]
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Link to="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-bold">Help Center</h1>
          <p className="text-xl text-muted-foreground mt-2">
            Find answers to your questions and get support
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {helpCategories.map((category) => (
            <Card key={category.title} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <category.icon className="h-8 w-8 text-primary" />
                  <div>
                    <CardTitle>{category.title}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {category.articles.map((article) => (
                    <li key={article}>
                      <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                        {article}
                      </a>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <h2 className="text-2xl font-semibold mb-4">Still need help?</h2>
          <p className="text-muted-foreground mb-6">
            Can't find what you're looking for? Get in touch with our support team.
          </p>
          <Link to="/contact">
            <Button>Contact Support</Button>
          </Link>
        </div>
      </div>
    </PageLayout>
  );
}
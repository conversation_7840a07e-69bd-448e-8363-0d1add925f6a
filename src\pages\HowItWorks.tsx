import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PageLayout } from "@/components/layout/PageLayout";

const HowItWorks = () => {
  const steps = [
    {
      icon: "fas fa-user-plus",
      title: "1. Create Your Account",
      description: "Sign up as a buyer or seller. Choose your path and set up your profile with your skills and interests."
    },
    {
      icon: "fas fa-search",
      title: "2. Browse or List",
      description: "Buyers can browse thousands of digital products and services. Sellers can create their first listing and showcase their work."
    },
    {
      icon: "fas fa-handshake",
      title: "3. Connect & Transact",
      description: "Find the perfect match for your needs. Our secure platform handles payments and ensures smooth transactions."
    },
    {
      icon: "fas fa-star",
      title: "4. Review & Grow",
      description: "Leave reviews, build your reputation, and grow your business or find exactly what you need for your next project."
    }
  ];

  const categories = [
    {
      icon: "fas fa-download",
      title: "Digital Products",
      description: "Templates, graphics, software, ebooks, and more ready-to-use digital assets."
    },
    {
      icon: "fas fa-tools",
      title: "Professional Services",
      description: "Web development, design, writing, marketing, and consulting services."
    },
    {
      icon: "fas fa-rocket",
      title: "Micro SaaS",
      description: "Small software solutions and tools that solve specific business problems."
    }
  ];

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            How EpicFreelancer Works
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Connecting creators and businesses through a seamless marketplace for digital products, services, and micro SaaS solutions.
          </p>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Getting Started is Easy
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-16 h-16 bg-gradient-primary text-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className={`${step.icon} text-2xl`}></i>
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{step.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            What You Can Find Here
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {categories.map((category, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-20 h-20 bg-gray-100 text-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className={`${category.icon} text-3xl`}></i>
                  </div>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{category.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of creators and businesses already using EpicFreelancer to grow their success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link to="/register">Start Selling</Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-primary" asChild>
              <Link to="/">Browse Marketplace</Link>
            </Button>
          </div>
        </div>
      </section>
    </PageLayout>
  );
};

export default HowItWorks;
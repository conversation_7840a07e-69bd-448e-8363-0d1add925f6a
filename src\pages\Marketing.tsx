import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PageLayout } from "@/components/layout/PageLayout";

const Marketing = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    {
      title: "SEO & Search Marketing",
      description: "Search engine optimization and SEM services",
      count: "1,600+ services"
    },
    {
      title: "Social Media Marketing",
      description: "Social media management and advertising",
      count: "2,200+ services"
    },
    {
      title: "Content Marketing",
      description: "Blog writing, copywriting, and content strategy",
      count: "1,800+ services"
    },
    {
      title: "Email Marketing",
      description: "Email campaigns and automation setup",
      count: "950+ services"
    },
    {
      title: "Digital Advertising",
      description: "Google Ads, Facebook Ads, and PPC management",
      count: "1,400+ services"
    },
    {
      title: "Marketing Strategy",
      description: "Marketing plans and growth strategies",
      count: "720+ services"
    }
  ];

  const featuredServices = [
    {
      title: "Complete SEO Optimization Package",
      startingPrice: "$299",
      rating: 4.9,
      reviews: 189,
      category: "SEO & Search Marketing",
      delivery: "14 days"
    },
    {
      title: "Social Media Management (30 days)",
      startingPrice: "$199",
      rating: 4.8,
      reviews: 256,
      category: "Social Media Marketing",
      delivery: "30 days"
    },
    {
      title: "Google Ads Campaign Setup",
      startingPrice: "$149",
      rating: 4.7,
      reviews: 134,
      category: "Digital Advertising",
      delivery: "7 days"
    },
    {
      title: "Content Marketing Strategy",
      startingPrice: "$399",
      rating: 4.9,
      reviews: 78,
      category: "Marketing Strategy",
      delivery: "10 days"
    }
  ];

  return (
    <PageLayout>
      <section className="py-16 px-4 bg-gradient-primary text-white">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Marketing Services
          </h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Grow your business with expert marketing professionals
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search marketing services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-32 py-4 text-lg rounded-full border-none bg-white text-gray-900"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
              <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 whitespace-nowrap">
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 py-16">
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Marketing Categories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary">{category.count}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Marketing Services</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredServices.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
                  <CardTitle className="text-lg line-clamp-2">{service.title}</CardTitle>
                  <CardDescription>
                    <Badge variant="outline" className="mb-2">{service.category}</Badge>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Starting at</span>
                      <span className="text-2xl font-bold text-primary">{service.startingPrice}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        {service.rating} ({service.reviews})
                      </div>
                      <span>{service.delivery}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Marketing;

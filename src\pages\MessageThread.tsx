import React, { useEffect, useState, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useNotification } from '@/contexts/NotificationContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';
import { DashboardHeader } from "@/components/dashboard/shared/DashboardHeader";

interface Message {
  content: string
  created_at: string
  id: string
  order_id: string | null
  read: boolean | null
  recipient_name: string
  recipient_id?: string | null
  sender_name: string
  sender_id?: string | null
  subject: string | null
}

const MessageThread = () => {
  const { userId } = useParams<{ userId: string }>();
  const { user, profile } = useAuth();
  const { clearNotificationsForUser } = useNotification();
  const [messages, setMessages] = useState<Message[]>([]);
  const [reply, setReply] = useState('');
  const [sending, setSending] = useState(false);
  const [hasMarkedAsRead, setHasMarkedAsRead] = useState(false);
  const navigate = useNavigate();
  const myId = profile?.user_id || user?.id;

  useEffect(() => {
    if (!myId || !userId) return;
    const fetchThread = async () => {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .or(`and(sender_id.eq.${myId},recipient_id.eq.${userId}),and(sender_id.eq.${userId},recipient_id.eq.${myId})`)
        .order('created_at', { ascending: true });
      if (!error && data) setMessages(data);
    };
    fetchThread();
  }, [myId, userId]);

  useEffect(() => {
    setHasMarkedAsRead(false);
  }, [userId, myId]);

  // Mark messages as read when conversation is opened
  useEffect(() => {
    if (!myId || !userId || messages.length === 0 || hasMarkedAsRead) return;
    const markAsRead = async () => {
      const unreadMessages = messages.filter(msg => 
        msg.recipient_id === myId && (msg.read === false || msg.read === null)
      );
      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map(msg => msg.id);
        const { error } = await supabase
          .from('messages')
          .update({ read: true })
          .in('id', messageIds);
        if (!error) {
          setMessages(prev => prev.map(msg => 
            messageIds.includes(msg.id) ? { ...msg, read: true } : msg
          ));
          // Clear notifications for this user
          clearNotificationsForUser(userId);
          console.log('Messages marked as read successfully');
        } else {
          console.error('Error marking messages as read:', error);
        }
      }
      
      // Mark that we've attempted to mark messages as read
      setHasMarkedAsRead(true);
    };
    
    markAsRead();
  }, [myId, userId, messages.length, hasMarkedAsRead, clearNotificationsForUser]);

  const otherName = useMemo(() => {
    if (!messages.length) return '';
    const first = messages.find(m => m.sender_id === userId) || messages[0];
    return first.sender_id === myId ? first.recipient_name : first.sender_name;
  }, [messages, myId, userId]);

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reply.trim() || !myId || !userId) return;
    setSending(true);
    const { error, data } = await supabase.from('messages').insert({
      sender_id: myId,
      sender_name: profile?.full_name || user?.email,
      recipient_id: userId,
      recipient_name: otherName,
      subject: '',
      content: reply,
      read: false,
      created_at: new Date().toISOString(),
    }).select().single();
    setSending(false);
    setReply('');
    if (!error && data) setMessages(prev => [...prev, data]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
    <DashboardHeader title="Message Thread" userInitials="JS" />
    <div className="flex flex-col h-[90vh] max-w-7xl mx-auto mt-8 border rounded bg-white relative">
      <div className="flex items-center gap-2 border-b p-3">
        <Avatar><AvatarFallback>{otherName[0]}</AvatarFallback></Avatar>
        <span className="font-semibold">{otherName}</span>
        <button className="ml-auto text-gray-400 hover:text-gray-700" onClick={() => navigate(-1)}>×</button>
      </div>
      <div className="flex-1 overflow-y-auto p-4 space-y-2 bg-gray-50">
        {messages.map((msg) => {
          const isMe = msg.sender_id === myId;
          return (
            <div key={msg.id} className={`flex ${isMe ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-xs rounded-lg px-3 py-2 ${isMe ? 'bg-blue-500 text-white' : 'bg-white border'}`}> 
                <div className="text-xs text-gray-300 mb-1">{new Date(msg.created_at).toLocaleString()}</div>
                <div className="font-medium">{msg.content}</div>
              </div>
            </div>
          );
        })}
      </div>
      <form className="flex items-center gap-2 border-t p-3" onSubmit={handleSend}>
        <Input
          value={reply}
          onChange={e => setReply(e.target.value)}
          placeholder="Type your message..."
          className="flex-1"
          disabled={sending}
        />
        <button type="submit" className="p-2 rounded-full bg-blue-500 text-white disabled:opacity-50" disabled={sending || !reply.trim()}>
          <Send className="w-5 h-5" />
        </button>
      </form>
    </div>
    </div>
  );
};

export default MessageThread; 
import React from 'react';
import { useSearchPara<PERSON>, <PERSON> } from 'react-router-dom';
import { PageLayout } from "@/components/layout/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLeft, ShoppingBag } from 'lucide-react';

const OrderCancelled = () => {
  const [searchParams] = useSearchParams();
  const productId = searchParams.get('product_id');

  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <XCircle className="w-20 h-20 text-orange-500" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Payment Cancelled
          </h1>
          <p className="text-xl text-gray-600">
            Your payment was cancelled. No charges were made to your account.
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>What Happened?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <span className="text-orange-600 font-semibold">1</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">No Charges Made</h3>
                <p className="text-gray-600">
                  Your payment was cancelled before completion. No money was taken from your account.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <span className="text-orange-600 font-semibold">2</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Try Again</h3>
                <p className="text-gray-600">
                  You can try the purchase again whenever you're ready. Your cart items are still saved.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <span className="text-orange-600 font-semibold">3</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Need Help?</h3>
                <p className="text-gray-600">
                  If you encountered any issues, our support team is here to help.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {productId && (
            <Button asChild size="lg" className="flex items-center gap-2">
              <Link to={`/product/${productId}`}>
                <ArrowLeft className="w-5 h-5" />
                Try Again
              </Link>
            </Button>
          )}
          <Button asChild variant="outline" size="lg" className="flex items-center gap-2">
            <Link to="/">
              <ShoppingBag className="w-5 h-5" />
              Continue Shopping
            </Link>
          </Button>
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-500">
            Having trouble? <Link to="/support" className="text-blue-600 hover:underline">Contact Support</Link>
          </p>
        </div>
      </div>
    </PageLayout>
  );
};

export default OrderCancelled; 
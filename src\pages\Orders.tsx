import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardHeader } from '@/components/dashboard/shared/DashboardHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useUserOrders } from '@/hooks/useSupabaseData';
import { Button } from '@/components/ui/button';
import ReviewFormModal from '@/components/dashboard/user/ReviewFormModal';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

const Orders = () => {
  const { user } = useAuth();
  const { data: orders = [], isLoading } = useUserOrders(user?.id);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [reviewsKey, setReviews<PERSON>ey] = useState(0);
  const { toast } = useToast();

  type Review = {
    listing_id: string;
    buyer_name: string;
  };
  
  const { data } = useQuery({
    queryKey: ['order-reviews', String(user?.id) + '-' + String(reviewsKey)],
    queryFn: async (): Promise<Review[]> => {
      if (!user?.id) return [];
      const { data, error } = await supabase
        .from('reviews')
        .select('listing_id, buyer_name')
        .eq('buyer_name', user.email || '');
      if (error) {
        toast({ title: 'Error', description: 'Failed to fetch reviews.' });
        return [];
      }
      return data || [];
    },
    enabled: !!user?.id
  });
  const reviewsArr = Array.isArray(data) ? data : [];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'in-progress':
        return <Badge className="bg-blue-500">In Progress</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Orders" userInitials="JS" />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
          <p className="text-gray-600">Track your purchases and order history</p>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Loading orders...</p>
          </div>
        ) : orders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No orders found.</p>
              <p className="text-sm text-gray-400 mt-2">Start shopping to see your orders here.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {orders.map(order => {
              const reviewed = reviewsArr.some(r => r.listing_id === order.listing_id);
              return (
                <Card key={order.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <h3 className="font-semibold text-lg">
                            {order.listings?.title || 'Unknown Product'}
                          </h3>
                          {getStatusBadge(order.status)}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>Seller: {order.profiles?.full_name || 'Unknown Seller'}</p>
                          <p>Order Date: {new Date(order.created_at).toLocaleDateString()}</p>
                          <p>Amount: ${order.amount}</p>
                          {order.notes && <p>Notes: {order.notes}</p>}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold text-green-600">${order.amount}</p>
                        {order.download_urls && order.download_urls.length > 0 && (
                          <div className="mt-2">
                            <p className="text-sm text-gray-600">Downloads:</p>
                            {order.download_urls.map((url, index) => (
                              <a
                                key={index}
                                href={url}
                                className="text-blue-600 hover:underline text-sm block"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                Download {index + 1}
                              </a>
                            ))}
                          </div>
                        )}
                        {order.status === 'completed' && !reviewed && (
                          <Button onClick={() => { setSelectedOrder(order); setReviewModalOpen(true); }}>
                            Leave a Review
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
      {reviewModalOpen && selectedOrder && (
        <ReviewFormModal 
          order={selectedOrder} 
          onClose={() => { setReviewModalOpen(false); setSelectedOrder(null); }} 
          onReviewSubmitted={() => setReviewsKey(k => k + 1)}
        />
      )}
    </div>
  );
};

export default Orders; 
import React from 'react';
import { ProductLayout } from '@/components/detail/shared/ProductLayout';
import { ProductHeader } from '@/components/detail/shared/ProductHeader';
import { MediaGallery } from '@/components/detail/shared/MediaGallery';
import { PricingSection } from '@/components/detail/shared/PricingSection';
import { SellerInfo } from '@/components/detail/shared/SellerInfo';
import { ProductInfo } from '@/components/detail/shared/ProductInfo';
import { DownloadInfo } from '@/components/detail/product/DownloadInfo';
import { useProductDetail } from '@/hooks/useProductDetail';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const ProductDetail = () => {
  const {
    product,
    reviews,
    seller,
    isLoading,
    avgRating,
    totalSales,
    isProductOwner,
    hasPurchased,
    isInWishlist,
    buyNowLoading,
    showPreviewModal,
    setShowPreviewModal,
    handleStripeCheckout,
    handleWishlist,
    handlePreview,
    handleShare,
    handleMessageSent,
  } = useProductDetail();

  if (isLoading) {
    return (
      <ProductLayout product={{ type: 'product', title: 'Loading...' }}>
        <div className="animate-pulse">
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="aspect-video bg-gray-200 rounded-lg mb-6"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            </div>
            <div className="space-y-6">
              <div className="h-32 bg-gray-200 rounded-lg"></div>
              <div className="h-48 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </ProductLayout>
    );
  }

  if (!product) {
    return (
      <ProductLayout product={{ type: 'product', title: 'Not Found' }}>
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-8">The product you're looking for doesn't exist or has been removed.</p>
          <Link to="/digital-products" className="text-blue-600 hover:text-blue-800 font-medium">
            ← Back to Digital Products
          </Link>
        </div>
      </ProductLayout>
    );
  }

  return (
    <ProductLayout product={product}>
      {/* Main Product Section */}
      <div className="grid lg:grid-cols-3 gap-8 mb-12">
        {/* Left Column - Image Gallery */}
        <div className="lg:col-span-2">
          <MediaGallery 
            images={product.gallery_urls || []} 
            title={product.title}
            mainImage={product.image_url}
            productType={product.type}
          />
        </div>
        
        {/* Right Column - Product Info & Pricing */}
        <div className="space-y-6">
          <ProductHeader 
            product={product}
            avgRating={avgRating}
            reviewCount={reviews.length}
          />

          <PricingSection
            product={product}
            onPurchase={handleStripeCheckout}
            loading={buyNowLoading}
            isOwner={isProductOwner}
            hasPurchased={hasPurchased}
          />

          {/* Seller Info */}
          {!isProductOwner && seller && (
            <SellerInfo
              seller={{
                id: seller.id,
                name: seller.full_name || product.seller_name,
                rating: avgRating,
                sales: totalSales,
                responseTime: '< 1 hour',
                memberSince: new Date(seller.created_at).getFullYear().toString()
              }}
              onContact={handleMessageSent}
              onFollow={() => {}}
            />
          )}
        </div>
      </div>

      {/* Product Details Section */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Description and Features */}
        <div className="lg:col-span-2 space-y-8">
          {/* Description */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Description</h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {product.description}
              </p>
            </div>
          </div>

          {/* Features */}
          {product.features && product.features.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">What's Included</h2>
              <div className="grid md:grid-cols-2 gap-3">
                {product.features.map((feature: string, index: number) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Technical Specifications */}
          {product.tech_specs && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-4">
                  {Object.entries(product.tech_specs).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
                      <span className="text-gray-900 font-medium">{value as string}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Download Information for Products */}
          {product.type === 'product' && (
            <DownloadInfo product={product} />
          )}

          {/* Reviews Section */}
          {reviews.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Customer Reviews</h2>
              <div className="space-y-6">
                {reviews.slice(0, 5).map((review: any) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {(review.buyer_name || 'A')[0].toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-semibold text-gray-900">{review.buyer_name}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <svg
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(review.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Additional Info */}
        <div className="space-y-6">
          <ProductInfo product={product} />
        </div>
      </div>
    </ProductLayout>
  );
};

export default ProductDetail;

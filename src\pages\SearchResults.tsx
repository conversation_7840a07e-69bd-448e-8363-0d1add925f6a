import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { PageLayout } from "@/components/layout/PageLayout";
import { ListingCard } from "@/components/homepage/ListingCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useListings, useCategories } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';

// Transform Supabase data to ListingCard format
const transformListing = (listing: any) => ({
  id: listing.id,
  slug: listing.slug,
  title: listing.title,
  seller: listing.seller_name || 'Unknown Seller',
  price: listing.price ? `$${listing.price}` : 'Free',
  rating: listing.rating || 0,
  reviews: listing.review_count || 0,
  category: listing.categories?.name,
  image: listing.image_url || '/placeholder-image.jpg',
  image_url: listing.image_url || '/placeholder-image.jpg',
  verified: listing.featured || false,
  type: listing.type || 'product',
});

const SearchResults = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12;
  
  const query = searchParams.get('query') || '';
  const { data: categories = [] } = useCategories();
  
  // Search listings with filters
  const { data: listings = [], isLoading } = useListings({
    type: selectedType || undefined,
    categoryId: selectedCategory || undefined,
    orderBy: sortBy === 'price' ? 'price' : sortBy === 'rating' ? 'rating' : sortBy === 'created_at' ? 'created_at' : undefined,
    orderDirection: sortBy === 'price' ? 'asc' : 'desc'
  });

  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  // Filter listings based on search query
  const filteredListings = listings.filter(listing => 
    listing.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    listing.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    listing.categories?.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const transformedListings = filteredListings.map(transformListing);
  const paginatedListings = transformedListings.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  const totalPages = Math.ceil(transformedListings.length / pageSize);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchParams({ query: searchQuery.trim() });
    }
  };

  const clearFilters = () => {
    setSelectedCategory('');
    setSelectedType('');
    setSortBy('relevance');
  };

  return (
    <PageLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Search Results for "{query}"
          </h1>
          
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-6">
            <div className="flex gap-4">
              <Input
                type="text"
                placeholder="Search for products, services, or tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
              />
              <Button type="submit">Search</Button>
            </div>
          </form>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Category:</span>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border rounded px-3 py-1 text-sm"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Type:</span>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="border rounded px-3 py-1 text-sm"
              >
                <option value="">All Types</option>
                <option value="product">Products</option>
                <option value="service">Services</option>
                <option value="tool">Tools</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border rounded px-3 py-1 text-sm"
              >
                <option value="relevance">Relevance</option>
                <option value="price">Price: Low to High</option>
                <option value="rating">Rating</option>
                <option value="created_at">Newest</option>
              </select>
            </div>

            {(selectedCategory || selectedType || sortBy !== 'relevance') && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">
            Found {transformedListings.length} result{transformedListings.length !== 1 ? 's' : ''}
          </p>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="w-full h-64 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        ) : transformedListings.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-4">No results found for "{query}"</p>
            <p className="text-gray-400">Try adjusting your search terms or filters</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paginatedListings.map((listing) => (
              <ListingCard key={listing.id} listing={listing} size="medium" />
            ))}
          </div>
        )}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8 gap-2">
            {Array.from({ length: totalPages }, (_, i) => (
              <button
                key={i}
                className={`px-3 py-1 rounded ${currentPage === i + 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700'}`}
                onClick={() => setCurrentPage(i + 1)}
              >
                {i + 1}
              </button>
            ))}
          </div>
        )}
      </div>
    </PageLayout>
  );
};

export default SearchResults; 
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  Package,
  ShoppingCart,
  MessageSquare,
  Star,
  DollarSign,
  Plus,
  Search
} from "lucide-react";
import { DashboardHeader } from "@/components/dashboard/shared/DashboardHeader";
import { AnalyticsMetrics } from "@/components/dashboard/seller/AnalyticsMetrics";
import { ProductsManagement } from "@/components/dashboard/seller/ProductsManagement";
import { MessagesList } from "@/components/dashboard/user/MessagesList";
import { useAuth } from '@/contexts/AuthContext';
import { useSellerOrders, useListings } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';
import ListingCreateForm from '@/components/dashboard/seller/ListingCreateForm';
import ListingEditForm from '@/components/dashboard/seller/ListingEditForm';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle
} from '@/components/ui/dialog';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

export const sellerNotifications = [
  {
    id: 1,
    title: 'New Message from John Doe',
    description: 'Logo revision request',
    messageId: 1,
    read: false,
  },
  {
    id: 2,
    title: 'New Message from Sarah Smith',
    description: 'Additional website features',
    messageId: 2,
    read: false,
  },
];
export const handleSellerNotificationClick = (notif, setActiveTab, setOpenMessageId, setNotifications) => {
  setActiveTab('messages');
  setOpenMessageId(notif.messageId);
  setNotifications((prev) => prev.map(n => n.id === notif.id ? { ...n, read: true } : n));
};

const SellerDashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDialog, setShowOrderDialog] = useState(false);
  const { user } = useAuth();
  const { data: orders = [], isLoading: ordersLoading } = useSellerOrders(user?.id);
  const { data: listings = [], isLoading: listingsLoading } = useListings(user ? {} : undefined);
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const defaultTab = searchParams.get('tab') || 'analytics';

  // Fetch reviews for seller's listings
  const { data: reviews = [], isLoading: reviewsLoading } = useQuery({
    queryKey: ['seller-reviews', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await supabase
        .from('reviews')
        .select(`
          id,
          rating,
          comment,
          created_at,
          buyer_name,
          listings!inner(
            id,
            title,
            seller_id
          )
        `)
        .eq('listings.seller_id', user.id)
        .order('created_at', { ascending: false });
      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id
  });

  // Analytics calculations
  const completedOrders = orders.filter(o => o.status === 'completed');
  const now = new Date();
  const thisMonthOrders = completedOrders.filter(o => {
    const d = new Date(o.created_at);
    return d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth();
  });
  const totalEarnings = completedOrders.reduce((sum, o) => sum + Number(o.amount || 0), 0);
  const totalCommission = completedOrders.reduce((sum, o) => sum + Number(o['commission'] || 0), 0);
  const netEarnings = totalEarnings - totalCommission;
  const thisMonth = thisMonthOrders.reduce((sum, o) => sum + Number(o.amount || 0), 0);
  const totalSales = completedOrders.length;
  const sellerListings = listings.filter(l => l.seller_id === user?.id);
  const activeListing = sellerListings.filter(l => l.status === 'active').length;
  // Calculate average rating from actual reviews
  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.length 
    : 0;
  // Profile views not tracked yet
  const profileViews = 0;
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editProduct, setEditProduct] = useState(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Payouts state
  const [payouts, setPayouts] = useState([]);
  const [payoutAmount, setPayoutAmount] = useState('');
  const [payoutLoading, setPayoutLoading] = useState(false);
  const [payoutError, setPayoutError] = useState('');
  const [payoutSuccess, setPayoutSuccess] = useState('');

  // Fetch payouts for this seller
  useEffect(() => {
    const fetchPayouts = async () => {
      if (!user?.id) return;
      const { data, error } = await supabase
        .from('payouts')
        .select('*')
        .eq('seller_id', user.id)
        .order('requested_at', { ascending: false });
      if (!error && data) setPayouts(data);
    };
    fetchPayouts();
  }, [user]);

  // Handle payout request
  const handlePayoutRequest = async () => {
    setPayoutError('');
    setPayoutSuccess('');
    setPayoutLoading(true);
    const amount = parseFloat(payoutAmount);
    if (isNaN(amount) || amount <= 0 || amount > netEarnings) {
      setPayoutError('Invalid payout amount');
      setPayoutLoading(false);
      return;
    }
    const { error } = await supabase
      .from('payouts')
      .insert([{ seller_id: user.id, amount }]);
    if (error) {
      setPayoutError('Failed to request payout');
    } else {
      setPayoutSuccess('Payout request submitted!');
      setPayoutAmount('');
      const { data } = await supabase
        .from('payouts')
        .select('*')
        .eq('seller_id', user.id)
        .order('requested_at', { ascending: false });
      if (data) setPayouts(data);
    }
    setPayoutLoading(false);
  };

  const analytics = {
    totalEarnings,
    totalCommission,
    netEarnings,
    thisMonth,
    totalSales,
    activeListing,
    averageRating,
    profileViews,
  };

  // Recent earnings: latest 3 completed orders
  const recentEarnings = completedOrders.slice(0, 3).map(o => ({
    date: o.created_at ? new Date(o.created_at).toISOString().slice(0, 10) : '',
    amount: o.amount,
    product: o.listings?.title || '',
    buyer: o.profiles?.full_name || '',
  }));

  // Map listings to products with real sales/revenue
  const products = sellerListings.map(listing => {
    const listingOrders = completedOrders.filter(o => o.listing_id === listing.id);
    const sales = listingOrders.length;
    const revenue = listingOrders.reduce((sum, o) => sum + Number(o.amount || 0), 0);
    return {
      id: listing.id || "0",
      title: listing.title || '',
      price: listing.price != null ? `$${listing.price}` : '$0',
      sales,
      revenue: `$${revenue}`,
      status: listing.status || '',
      image_url: listing.image_url || '/placeholder-image.jpg',
      views: 0, // Not tracked yet
      conversion: 'N/A', // Not tracked yet
    };
  });

  console.log("products", products, sellerListings);

  const [messages, setMessages] = useState<any[]>([]);
  useEffect(() => {
    if (!user) return;
    const fetchMessages = async () => {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .order('created_at', { ascending: false });
        console.log(">>>>>>>>>>>. data messages", data);
      if (!error && data) setMessages(data);
    };

    fetchMessages();
  }, [user]);

  // Refresh messages when returning to dashboard
  useEffect(() => {
    const handleFocus = () => {
      if (user) {
        const fetchMessages = async () => {
          const { data, error } = await supabase
            .from('messages')
            .select('*')
            .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
            .order('created_at', { ascending: false });
          if (!error && data) setMessages(data);
        };
        fetchMessages();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [user]);

  useEffect(() => {
    const handleStorage = (e: StorageEvent) => {
      if (e.key === 'refreshMessages') {
        if (user) {
          const fetchMessages = async () => {
            const { data, error } = await supabase
              .from('messages')
              .select('*')
              .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
              .order('created_at', { ascending: false });
            if (!error && data) setMessages(data);
          };
          fetchMessages();
        }
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, [user]);

  const handleRead = (id: string) => {
    setMessages((msgs) => msgs.map((m) => m.id === id ? { ...m, unread: false } : m));
  };
  const handleMarkAllRead = () => {
    setMessages((msgs) => msgs.map((m) => ({ ...m, unread: false })));
  };

  // Simulate receiving a new message (for demo)
  const handleSimulateNewMessage = () => {
    const newMsg = {
      id: messages.length + 1,
      from: "System Bot",
      subject: "New message received!",
      preview: "This is a simulated new message.",
      timestamp: "Just now",
      unread: true
    };
    setMessages((msgs) => [newMsg, ...msgs]);
    toast({
      title: "New Message",
      description: `From: ${newMsg.from} - ${newMsg.subject}`,
    });
  };



  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  // Calculate available payout amount (net earnings minus pending payouts)
  const pendingPayouts = payouts.filter(p => p.status === 'pending');
  const totalPendingPayouts = pendingPayouts.reduce((sum, p) => sum + Number(p.amount), 0);
  const availableForPayout = netEarnings - totalPendingPayouts;



  const [activeTab, setActiveTab] = useState(defaultTab);
  const [openMessageId, setOpenMessageId] = useState<string | null>(null);
  const userId = user?.id;
  const filteredMessages = messages.filter(
    (msg, idx, arr) =>
      (msg.sender_id === userId || msg.recipient_id === userId) &&
      arr.findIndex(m => m.id === msg.id) === idx
  );

  // Conversation grouping for sidebar
  const conversations = React.useMemo(() => {
    const map = new Map();
    filteredMessages.forEach((msg) => {
      const otherId = msg.sender_id === userId ? msg.recipient_id : msg.sender_id;
      if (!map.has(otherId)) map.set(otherId, []);
      map.get(otherId).push(msg);
    });
    return Array.from(map.entries()).map(([otherId, msgs]) => {
      const lastMsg = msgs[0];
      // Only count messages that are actually unread (read: false or null)
      const unreadCount = msgs.filter(msg => 
        msg.recipient_id === userId && (msg.read === false || msg.read === null)
      ).length;
      return {
        otherId,
        otherName: lastMsg.sender_id === userId ? lastMsg.recipient_name : lastMsg.sender_name,
        otherAvatar: '/placeholder.svg',
        lastSubject: lastMsg.subject,
        lastDate: lastMsg.created_at,
        unreadCount,
        hasUnread: unreadCount > 0,
      };
    });
  }, [filteredMessages, userId]);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Seller Dashboard" userInitials="JS" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Seller Dashboard</h1>
          <p className="text-gray-600">Manage your products, orders, and business analytics</p>
        </div>

        <Tabs defaultValue={defaultTab} value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="products" className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Products
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center gap-2">
              <ShoppingCart className="w-4 h-4" />
              Orders
            </TabsTrigger>
            <TabsTrigger value="messages" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Messages
            </TabsTrigger>
            <TabsTrigger value="reviews" className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              Reviews
            </TabsTrigger>
            <TabsTrigger value="payouts" className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Payouts
            </TabsTrigger>
          </TabsList>

          <TabsContent value="analytics" className="space-y-6">
            <h2 className="text-2xl font-semibold">Analytics & Earnings</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Gross Earnings</span>
                    <span className="text-2xl font-bold text-green-700">${totalEarnings.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Total Commission</span>
                    <span className="text-2xl font-bold text-red-600">-${totalCommission.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Net Earnings</span>
                    <span className="text-2xl font-bold text-blue-700">${netEarnings.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
            <AnalyticsMetrics analytics={analytics} />
            <Card>
              <CardHeader>
                <CardTitle>Recent Earnings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentEarnings.map((earning, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{earning.product}</p>
                        <p className="text-sm text-gray-600">by {earning.buyer}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">${earning.amount}</p>
                        <p className="text-sm text-gray-500">{earning.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            <div className="mb-4">
              {reviews.length > 0 && (
                <div className="font-bold">Seller Rating: {averageRating.toFixed(2)}★ ({reviews.length} reviews)</div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="products" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Product Management</h2>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Add New Product
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <span className="sr-only">
                    <DialogTitle>Add New Product</DialogTitle>
                  </span>
                  <ListingCreateForm />
                </DialogContent>
              </Dialog>
            </div>
            <ProductsManagement
              products={products}
              onEdit={(product) => {
                setEditProduct(product);
                setShowEditDialog(true);
              }}
            />
            <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
              <DialogContent>
                <span className="sr-only">
                  <DialogTitle>Edit Product</DialogTitle>
                </span>
                {editProduct && (
                  <ListingEditForm
                    listing={editProduct}
                    onUpdated={() => setShowEditDialog(false)}
                  />
                )}
              </DialogContent>
            </Dialog>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Orders & Requests</h2>
              <div className="relative w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search orders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-4">
              {ordersLoading ? (
                <div className="text-center text-gray-500">Loading orders...</div>
              ) : orders.length === 0 ? (
                <div className="text-center text-gray-500">No orders found.</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Buyer</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orders.map((order) => (
                        <tr key={order.id}>
                          <td className="px-4 py-2 whitespace-nowrap">{order.listings?.title || ''}</td>
                          <td className="px-4 py-2 whitespace-nowrap">{order.profiles?.full_name || ''}</td>
                          <td className="px-4 py-2 whitespace-nowrap">${order.amount}</td>
                          <td className="px-4 py-2 whitespace-nowrap capitalize">{order.status}</td>
                          <td className="px-4 py-2 whitespace-nowrap">{order.created_at ? new Date(order.created_at).toLocaleDateString() : ''}</td>
                          <td className="px-4 py-2 whitespace-nowrap">
                            <button
                              className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs mr-2"
                              onClick={() => {
                                setSelectedOrder(order);
                                setShowOrderDialog(true);
                              }}
                            >
                              View Details
                            </button>
                            {order.status !== 'completed' && (
                              <button
                                className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs"
                                onClick={async () => {
                                  // TODO: Replace with backend call
                                  alert('Mark as complete: ' + order.id);
                                  // await supabase.from('orders').update({ status: 'completed' }).eq('id', order.id);
                                }}
                              >
                                Mark as Complete
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="messages" className="space-y-6">
            <h2 className="text-2xl font-semibold">Messages</h2>
            <div className="space-y-2">
              {conversations.length === 0 && <div className="text-gray-500 text-center py-8">No conversations yet.</div>}
              {conversations.map(convo => (
                <div key={convo.otherId} className="flex items-center gap-3 p-3 border rounded cursor-pointer hover:bg-gray-50" onClick={() => navigate(`/messages/${convo.otherId}`)}>
                  <Avatar><AvatarFallback>{convo.otherName[0]}</AvatarFallback></Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <div className={`font-semibold ${convo.hasUnread ? 'font-bold' : ''}`}>{convo.otherName}</div>
                      {convo.hasUnread && (
                        <div className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {convo.unreadCount}
                        </div>
                      )}
                    </div>
                    <div className={`text-sm truncate ${convo.hasUnread ? 'font-medium text-gray-900' : 'text-gray-600'}`}>{convo.lastSubject}</div>
                  </div>
                  <div className="text-xs text-gray-400">{new Date(convo.lastDate).toLocaleDateString()}</div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-6">
            <h2 className="text-2xl font-semibold">Customer Reviews</h2>
            <div className="grid gap-4">
              {reviewsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading reviews...</p>
                </div>
              ) : reviews.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-gray-500">No reviews yet. Reviews will appear here once customers leave feedback on your listings.</p>
                  </CardContent>
                </Card>
              ) : (
                reviews.map((review) => (
                  <Card key={review.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold">{review.buyer_name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{review.listings.title}</p>
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="flex">{renderStars(review.rating)}</div>
                            <span className="text-sm text-gray-600">({review.rating}/5)</span>
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                        </div>
                        <span className="text-sm text-gray-500">{new Date(review.created_at).toLocaleDateString()}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="payouts" className="space-y-6">
            <h2 className="text-2xl font-semibold">Payouts</h2>
            <Card>
              <CardContent className="p-6 space-y-6">
                <div className="flex flex-col md:flex-row md:items-center md:gap-6 gap-4">
                  <div className="flex-1">
                    <span className="block text-gray-600 mb-1">Available for payout:</span>
                    <span className="text-2xl font-bold text-blue-700">${availableForPayout.toFixed(2)}</span>
                  </div>
                  <div className="flex-1">
                    <label className="block text-gray-600 mb-1">Request Payout</label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        min="1"
                        max={availableForPayout}
                        value={payoutAmount}
                        onChange={e => setPayoutAmount(e.target.value)}
                        placeholder="Amount"
                        className="w-32"
                      />
                      <Button onClick={handlePayoutRequest} disabled={payoutLoading || !payoutAmount || parseFloat(payoutAmount) > availableForPayout}>
                        {payoutLoading ? 'Requesting...' : 'Request Payout'}
                      </Button>
                    </div>
                    {payoutError && <div className="text-red-600 text-sm mt-1">{payoutError}</div>}
                    {payoutSuccess && <div className="text-green-600 text-sm mt-1">{payoutSuccess}</div>}
                  </div>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-2">Payout History</h3>
                  {payouts.length === 0 ? (
                    <div className="text-gray-500">No payout requests yet.</div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Requested At</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Processed At</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {payouts.map((payout: any) => (
                            <tr key={payout.id}>
                              <td className="px-4 py-2 whitespace-nowrap">${payout.amount.toFixed(2)}</td>
                              <td className="px-4 py-2 whitespace-nowrap capitalize">{payout.status}</td>
                              <td className="px-4 py-2 whitespace-nowrap">{payout.requested_at ? new Date(payout.requested_at).toLocaleString() : ''}</td>
                              <td className="px-4 py-2 whitespace-nowrap">{payout.processed_at ? new Date(payout.processed_at).toLocaleString() : '-'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Dialog open={showOrderDialog} onOpenChange={setShowOrderDialog}>
        <DialogContent>
          <span className="sr-only">
            <DialogTitle>Order Details</DialogTitle>
          </span>
          {selectedOrder && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold mb-2">Order Details</h3>
              <div><b>Product:</b> {selectedOrder.listings?.title || ''}</div>
              <div><b>Buyer:</b> {selectedOrder.profiles?.full_name || ''}</div>
              <div><b>Amount:</b> ${selectedOrder.amount}</div>
              <div><b>Status:</b> {selectedOrder.status}</div>
              <div><b>Date:</b> {selectedOrder.created_at ? new Date(selectedOrder.created_at).toLocaleDateString() : ''}</div>
              {selectedOrder.notes && <div><b>Notes:</b> {selectedOrder.notes}</div>}
              {selectedOrder.delivery_date && <div><b>Delivery Date:</b> {new Date(selectedOrder.delivery_date).toLocaleDateString()}</div>}
              {selectedOrder.download_urls && selectedOrder.download_urls.length > 0 && (
                <div><b>Downloads:</b> <ul className="list-disc ml-6">{selectedOrder.download_urls.map((url, i) => <li key={i}><a href={url} className="text-blue-600 underline" target="_blank" rel="noopener noreferrer">Download {i+1}</a></li>)}</ul></div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SellerDashboard;

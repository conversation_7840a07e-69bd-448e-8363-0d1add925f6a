import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { useToast } from "@/hooks/use-toast";
import { Check } from 'lucide-react';
import { OnboardingHeader } from "@/components/onboarding/seller/OnboardingHeader";
import { ProgressBar } from "@/components/onboarding/seller/ProgressBar";
import { BusinessProfile } from "@/components/onboarding/seller/steps/BusinessProfile";
import { ContactInfo } from "@/components/onboarding/seller/steps/ContactInfo";
import { PaymentSetup } from "@/components/onboarding/seller/steps/PaymentSetup";
import { FirstListing } from "@/components/onboarding/seller/steps/FirstListing";
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useRef } from 'react';
import { useCategories } from '@/hooks/useSupabaseData';
import { generateSlug } from '@/lib/utils';

const SellerOnboarding = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    businessName: '',
    description: '',
    category: '',
    contactEmail: '',
    phone: '',
    paypalEmail: '',
    productTitle: '',
    productDescription: '',
    price: '',
    productCategory: ''
  });
  
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const { data: categories = [], isLoading: categoriesLoading } = useCategories();

  const businessProfileRef = useRef<any>(null);
  const contactInfoRef = useRef<any>(null);
  const paymentSetupRef = useRef<any>(null);
  const firstListingRef = useRef<any>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    let valid = true;
    if (currentStep === 1 && businessProfileRef.current) {
      valid = businessProfileRef.current.validate();
    } else if (currentStep === 2 && contactInfoRef.current) {
      valid = contactInfoRef.current.validate();
    } else if (currentStep === 3 && paymentSetupRef.current) {
      valid = paymentSetupRef.current.validate();
    }
    if (!valid) return;
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (firstListingRef.current && !firstListingRef.current.validate()) {
      return;
    }
    // Mark user as seller in DB
    if (user) {
      const updateResponse = await supabase
        .from('profiles')
        .update({ is_seller: true })
        .eq('user_id', user.id);
      if (updateResponse.error) {
        toast({
          title: "Error",
          description: "Failed to mark user as seller.",
        });
        return;
      }
    }
    // Save first listing to DB
    if (user && formData.productTitle && formData.productDescription && formData.price && formData.productCategory) {
      const listing = {
        title: formData.productTitle,
        slug: generateSlug(formData.productTitle),
        description: formData.productDescription,
        price: parseFloat(formData.price),
        category_id: formData.productCategory,
        seller_id: user.id, // required by schema
        seller_name: profile?.full_name || user.user_metadata?.full_name || user.email,
        type: 'product', // or 'service'/'tool' if you have a type selector
        status: 'active',
        created_at: new Date().toISOString(),
      };
      const insertResponse = await supabase
        .from('listings')
        .insert([listing]);
      
      console.log("insertResponse", insertResponse);
      if (insertResponse.error) {
        toast({
          title: "Error",
          description: "Failed to save first listing.",
        });
        return;
      }
    }
    toast({
      title: "Application Submitted!",
      description: "We'll review your application and get back to you within 24 hours.",
    });
    setTimeout(() => {
      navigate('/seller-dashboard');
    }, 2000);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <BusinessProfile
            ref={businessProfileRef}
            formData={formData}
            categories={categories}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <ContactInfo
            ref={contactInfoRef}
            formData={formData}
            onInputChange={handleInputChange}
          />
        );
      case 3:
        return (
          <PaymentSetup
            ref={paymentSetupRef}
            formData={formData}
            onInputChange={handleInputChange}
          />
        );
      case 4:
        return (
          <FirstListing
            ref={firstListingRef}
            formData={formData}
            categories={categories}
            onInputChange={handleInputChange}
          />
        );
      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Business Profile';
      case 2: return 'Contact Info';
      case 3: return 'Payment Setup';
      case 4: return 'First Listing';
      default: return '';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <OnboardingHeader />
      <ProgressBar currentStep={currentStep} totalSteps={4} stepTitle={getStepTitle()} />

      <div className="max-w-2xl mx-auto px-4 py-8">
        {renderStep()}
        
        <div className="flex justify-between mt-8">
          <Button 
            variant="outline" 
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            Previous
          </Button>
          
          {currentStep === 4 ? (
            <Button 
              onClick={handleSubmit}
              className="bg-gradient-primary hover:opacity-90"
            >
              <Check className="h-4 w-4 mr-2" />
              Submit Application
            </Button>
          ) : (
            <Button 
              onClick={handleNext}
              className="bg-gradient-primary hover:opacity-90"
            >
              Continue
            </Button>
          )}
        </div>
        
        <div className="text-center mt-6">
          <p className="text-sm text-muted-foreground">
            Need help? <a href="#" className="text-primary hover:underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SellerOnboarding;

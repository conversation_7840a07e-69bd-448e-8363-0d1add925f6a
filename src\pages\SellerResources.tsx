import { <PERSON><PERSON><PERSON>t, BookO<PERSON>, DollarSign, TrendingUp, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { PageLayout } from "@/components/layout/PageLayout";

export default function SellerResources() {
  const resources = [
    {
      title: "Seller Handbook",
      description: "Complete guide to selling successfully on EpicFreelancer",
      icon: BookOpen,
      items: ["Profile optimization", "Pricing strategies", "Customer communication"]
    },
    {
      title: "Earnings & Payments",
      description: "Everything about payments, fees, and maximizing earnings",
      icon: DollarSign,
      items: ["Fee structure", "Payment schedules", "Tax information"]
    },
    {
      title: "Marketing Your Services",
      description: "Grow your business and attract more customers",
      icon: TrendingUp,
      items: ["SEO optimization", "Portfolio building", "Social media promotion"]
    },
    {
      title: "Community & Support",
      description: "Connect with other sellers and get help",
      icon: Users,
      items: ["Seller forums", "Success stories", "Expert webinars"]
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Link to="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-bold">Seller Resources</h1>
          <p className="text-xl text-muted-foreground mt-2">
            Everything you need to succeed as a seller
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {resources.map((resource) => (
            <Card key={resource.title} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <resource.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <CardTitle>{resource.title}</CardTitle>
                    <CardDescription>{resource.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {resource.items.map((item) => (
                    <li key={item} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                      <span className="text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Start Selling?</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join thousands of successful sellers on EpicFreelancer and start earning money from your skills and expertise.
          </p>
          <div className="flex gap-4 justify-center">
            <Link to="/seller-onboarding">
              <Button size="lg">Become a Seller</Button>
            </Link>
            <Link to="/help">
              <Button variant="outline" size="lg">Learn More</Button>
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
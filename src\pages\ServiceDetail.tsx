import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from "@/components/layout/PageLayout";
import { DetailHero } from "@/components/detail/shared/DetailHero";
import { AuthorCard } from "@/components/detail/shared/AuthorCard";
import { PackageSelector } from "@/components/detail/service/PackageSelector";
import { DetailTabs } from "@/components/detail/shared/DetailTabs";
import { FeaturesList } from "@/components/detail/shared/FeaturesList";
import { ReviewsSection } from "@/components/detail/shared/ReviewsSection";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useListing, useListingReviews } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { StripeClient } from '@/integrations/stripe/client';

// Inline useProfile hook
const useProfile = (id?: string) => {
  return useQuery({
    queryKey: ['profile', id],
    queryFn: async () => {
      if (!id) return null;
      const { data, error } = await supabase
        .from('profiles')
        .select('*, listings(*), reviews:reviews(*, profiles(*))')
        .eq('id', id)
        .single();
      if (error) throw error;
      return data;
    },
    enabled: !!id
  });
};

const ServiceDetail = () => {
  const { identifier } = useParams<{ identifier: string }>();
  const { data: service, isLoading } = useListing(identifier || '');
  const sellerId = service?.seller_id;
  const { data: seller } = useProfile(sellerId);
  const { data: reviews, isLoading: reviewsLoading } = useListingReviews(service?.id || '');
  const sellerReviews = seller?.reviews || [];
  const totalSales = sellerReviews.length;
  const avgRating = sellerReviews.length ? sellerReviews.reduce((sum, r) => sum + (r.rating || 0), 0) / sellerReviews.length : 0;
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const [buyerInfo, setBuyerInfo] = useState({
    fullName: user?.user_metadata?.full_name || '',
    email: user?.email || '',
    phone: '',
    requirements: ''
  });

  const handlePurchase = () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to purchase this service.",
      });
      return;
    }
    setShowPurchaseModal(true);
  };

  const handleStripeCheckout = async () => {
    if (!user || !service) {
      toast({
        title: "Error",
        description: "Please log in first",
      });
      return;
    }

    try {
      const baseUrl = window.location.origin;
      console.log(">>>>>>>>>> base url >>>>>>>", baseUrl)
      // const baseUrl = "https://jecfmpvnvpnblzkxqjys.supabase.co/"
      await StripeClient.initiatePurchase({
        price: service.price || 0,
        productName: service.title,
        productId: service.id,
        buyerEmail: user.email || '',
        successUrl: `${baseUrl}/order-confirmation?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${baseUrl}/order-cancelled?product_id=${service.id}`,
      });
    } catch (error) {
      console.error('Stripe checkout error:', error);
      toast({
        title: "Checkout Error",
        description: "Failed to initiate checkout. Please try again.",
      });
    }
  };

  const handlePurchaseSubmit = async () => {
    try {
      const orderData = {
        listing_id: service?.id,
        buyer_id: user?.id,
        seller_id: service?.seller_id,
        amount: service?.price || 0,
        status: 'pending',
        notes: buyerInfo.requirements,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('orders')
        .insert([orderData]);

      if (error) throw error;

      toast({
        title: "Order Created!",
        description: "Your service order has been placed successfully.",
      });

      setShowPurchaseModal(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create order. Please try again.",
      });
    }
  };

  if (isLoading) {
    return <div className="max-w-7xl mx-auto px-4 py-8">Loading...</div>;
  }
  if (!service) {
    return <div className="max-w-7xl mx-auto px-4 py-8">Service not found.</div>;
  }

  const tabs = [
    {
      value: "description",
      label: "Description",
      content: service.description || ''
    },
    {
      value: "includes",
      label: "What's Included",
      content: <FeaturesList features={service.features || []} type="simple" />
    },
    {
      value: "reviews",
      label: "Reviews",
      content: reviewsLoading ? (
        <div>Loading reviews...</div>
      ) : !reviews || reviews.length === 0 ? (
        <div>No reviews yet.</div>
      ) : (
        <div className="space-y-6">
          <div className="mb-4">
            <span className="font-bold">Average Rating:</span> {avgRating.toFixed(1)} / 5 ({reviews.length} reviews)
          </div>
          {reviews.map((review: any) => (
            <div key={review.id} className="border-b pb-4 last:border-b-0">
              <div className="flex items-center gap-4 mb-2">
                <img src="/placeholder.svg" alt="User" className="w-10 h-10 rounded-full" />
                <div>
                  <h4 className="font-semibold">{review.profiles?.full_name || 'Anonymous'}</h4>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className={`w-4 h-4 rounded-full ${i < review.rating ? 'bg-yellow-500' : 'bg-gray-200'}`}></div>
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-700">{review.comment}</p>
            </div>
          ))}
        </div>
      )
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <DetailHero
              category={service.categories?.name || ''}
              title={service.title}
              rating={service.rating}
              reviews={service.review_count}
              completedOrders={0} // Not tracked yet
              startingPrice={service.price ? `$${service.price}` : ''}
              price=""
              tags={service.tags || []}
            />

            <div className="aspect-video bg-gray-200 rounded-lg"></div>

            <DetailTabs tabs={tabs} defaultValue="description" />
          </div>

          <div className="space-y-6">
            <AuthorCard 
              author={{
                name: seller?.full_name || 'Unknown Seller',
                avatar: '/placeholder.svg',
                rating: avgRating,
                sales: totalSales
              }}
              showContactButton={true}
              showActions={true}
            />

            <Card>
              <CardHeader>
                <CardTitle>Service Package</CardTitle>
                <CardDescription>Choose your service option</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-semibold">Standard Package</h3>
                      <span className="text-lg font-bold text-green-600">
                        ${service.price}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      {service.description}
                    </p>
                    <Button onClick={handleStripeCheckout} className="w-full">
                      Order Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <Dialog open={showPurchaseModal} onOpenChange={setShowPurchaseModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Order Service</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="fullName">Full Name</Label>
                <Input
                  id="fullName"
                  value={buyerInfo.fullName}
                  onChange={(e) => setBuyerInfo(prev => ({ ...prev, fullName: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={buyerInfo.email}
                  onChange={(e) => setBuyerInfo(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={buyerInfo.phone}
                  onChange={(e) => setBuyerInfo(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="requirements">Project Requirements</Label>
                <Textarea
                  id="requirements"
                  value={buyerInfo.requirements}
                  onChange={(e) => setBuyerInfo(prev => ({ ...prev, requirements: e.target.value }))}
                  placeholder="Describe your project requirements..."
                />
              </div>
              <div className="flex justify-between items-center pt-4">
                <div>
                  <p className="font-semibold">Total: ${service.price}</p>
                </div>
                <div className="space-x-2">
                  <Button variant="outline" onClick={() => setShowPurchaseModal(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handlePurchaseSubmit}>
                    Place Order
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </PageLayout>
  );
};

export default ServiceDetail;
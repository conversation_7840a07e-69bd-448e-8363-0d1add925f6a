import React, { useState } from 'react';
import { ProductLayout } from '@/components/detail/shared/ProductLayout';
import { ProductHeader } from '@/components/detail/shared/ProductHeader';
import { MediaGallery } from '@/components/detail/shared/MediaGallery';
import { PricingSection } from '@/components/detail/shared/PricingSection';
import { SellerInfo } from '@/components/detail/shared/SellerInfo';
import { ProductInfo } from '@/components/detail/shared/ProductInfo';
import { ServicePackages } from '@/components/detail/service/ServicePackages';
import { RequirementsForm } from '@/components/detail/service/RequirementsForm';
import { useProductDetail } from '@/hooks/useProductDetail';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Clock, RotateCcw, CheckCircle, MessageSquare } from 'lucide-react';

const ServiceDetail = () => {
  const {
    product: service,
    reviews,
    seller,
    isLoading,
    avgRating,
    totalSales,
    isProductOwner,
    hasPurchased,
    isInWishlist,
    buyNowLoading,
    showPreviewModal,
    setShowPreviewModal,
    handleStripeCheckout,
    handleWishlist,
    handlePreview,
    handleShare,
    handleMessageSent,
  } = useProductDetail();

  const [showRequirementsForm, setShowRequirementsForm] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<string>('standard');

  const handleSelectPackage = (packageId: string, price: number) => {
    setSelectedPackage(packageId);
    // Update the service price if needed
  };

  const handleOrderService = () => {
    if (!service?.requires_requirements) {
      handleStripeCheckout();
    } else {
      setShowRequirementsForm(true);
    }
  };

  const handleRequirementsSubmit = (requirements: any) => {
    // Process requirements and then proceed to checkout
    setShowRequirementsForm(false);
    handleStripeCheckout();
  };

  if (isLoading) {
    return (
      <ProductLayout product={{ type: 'service', title: 'Loading...' }}>
        <div className="animate-pulse">
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="aspect-video bg-gray-200 rounded-lg mb-6"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            </div>
            <div className="space-y-6">
              <div className="h-32 bg-gray-200 rounded-lg"></div>
              <div className="h-48 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </ProductLayout>
    );
  }

  if (!service) {
    return (
      <ProductLayout product={{ type: 'service', title: 'Not Found' }}>
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Service Not Found</h1>
          <p className="text-gray-600 mb-8">The service you're looking for doesn't exist or has been removed.</p>
          <Link to="/services" className="text-blue-600 hover:text-blue-800 font-medium">
            ← Back to Services
          </Link>
        </div>
      </ProductLayout>
    );
  }

  return (
    <ProductLayout product={service}>
      {/* Main Service Section */}
      <div className="grid lg:grid-cols-3 gap-8 mb-12">
        {/* Left Column - Portfolio Gallery */}
        <div className="lg:col-span-2">
          <MediaGallery 
            images={service.gallery_urls || []} 
            title={service.title}
            mainImage={service.image_url}
            productType="service"
          />
        </div>
        
        {/* Right Column - Service Info & Pricing */}
        <div className="space-y-6">
          <ProductHeader 
            product={service}
            avgRating={avgRating}
            reviewCount={reviews.length}
          />

          {/* Service Highlights */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-green-600" />
                <span className="text-green-800">
                  {service.delivery_time ? `${service.delivery_time} days` : 'Custom timeline'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4 text-green-600" />
                <span className="text-green-800">
                  {service.revisions === 999 ? 'Unlimited' : `${service.revisions || 2}`} revisions
                </span>
              </div>
            </div>
          </div>

          <PricingSection
            product={service}
            onPurchase={handleOrderService}
            loading={buyNowLoading}
            isOwner={isProductOwner}
            hasPurchased={hasPurchased}
          />

          {/* Seller Info */}
          {!isProductOwner && seller && (
            <SellerInfo
              seller={{
                id: seller.id,
                name: seller.full_name || service.seller_name,
                rating: avgRating,
                sales: totalSales,
                responseTime: '< 2 hours',
                memberSince: new Date(seller.created_at).getFullYear().toString()
              }}
              onContact={handleMessageSent}
              onFollow={() => {}}
            />
          )}
        </div>
      </div>

      {/* Service Details Section */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Description, Packages, and Features */}
        <div className="lg:col-span-2 space-y-8">
          {/* Description */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">About This Service</h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {service.description}
              </p>
            </div>
          </div>

          {/* Service Packages */}
          <ServicePackages
            defaultPrice={service.price}
            defaultDeliveryTime={service.delivery_time}
            defaultRevisions={service.revisions}
            features={service.features}
            onSelectPackage={handleSelectPackage}
          />

          {/* What You Get */}
          {service.features && service.features.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">What You Get</h2>
              <div className="grid md:grid-cols-2 gap-3">
                {service.features.map((feature: string, index: number) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Process */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">How It Works</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Share Your Requirements</h3>
                  <p className="text-gray-600 text-sm">Tell me about your project goals, timeline, and any specific needs.</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h3 className="font-semibold text-gray-900">I Get to Work</h3>
                  <p className="text-gray-600 text-sm">I'll start working on your project and keep you updated on progress.</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Review & Refine</h3>
                  <p className="text-gray-600 text-sm">Review the work and request revisions until you're completely satisfied.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          {reviews.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Client Reviews</h2>
              <div className="space-y-6">
                {reviews.slice(0, 5).map((review: any) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {(review.buyer_name || 'A')[0].toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-semibold text-gray-900">{review.buyer_name}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <svg
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(review.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Additional Info */}
        <div className="space-y-6">
          <ProductInfo product={service} />
        </div>
      </div>

      {/* Requirements Form Modal */}
      <RequirementsForm
        isOpen={showRequirementsForm}
        onClose={() => setShowRequirementsForm(false)}
        onSubmit={handleRequirementsSubmit}
        service={service}
      />
    </ProductLayout>
  );
};

export default ServiceDetail;

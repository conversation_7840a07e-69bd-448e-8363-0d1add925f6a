import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PageLayout } from "@/components/layout/PageLayout";
import { useCategories, useListings } from '@/hooks/useSupabaseData';
import { Loader2, Star, Filter, SortAsc } from 'lucide-react';

const Services = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('featured');
  const [priceRange, setPriceRange] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 12;

  // Fetch data from Supabase
  const { data: categories, isLoading: categoriesLoading } = useCategories();
  const { data: allListings, isLoading: listingsLoading } = useListings({
    type: 'service',
    status: 'active'
  });

  // Calculate service counts per category
  const categoriesWithCounts = useMemo(() => {
    if (!categories || !allListings) return [];

    return categories.map(category => {
      const count = allListings.filter(listing => listing.category_id === category.id).length;
      return {
        ...category,
        count: `${count} service${count !== 1 ? 's' : ''}`
      };
    });
  }, [categories, allListings]);

  // Filter and sort listings
  const filteredAndSortedListings = useMemo(() => {
    if (!allListings) return [];

    let filtered = allListings.filter(listing => {
      // Search filter
      if (searchQuery && !listing.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !listing.description?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (selectedCategory !== 'all' && listing.category_id !== selectedCategory) {
        return false;
      }

      // Price range filter
      if (priceRange !== 'all' && listing.price) {
        const price = parseFloat(listing.price.toString());
        switch (priceRange) {
          case 'under-100':
            if (price >= 100) return false;
            break;
          case '100-500':
            if (price < 100 || price > 500) return false;
            break;
          case '500-1000':
            if (price < 500 || price > 1000) return false;
            break;
          case 'over-1000':
            if (price <= 1000) return false;
            break;
        }
      }

      return true;
    });

    // Sort listings
    switch (sortBy) {
      case 'featured':
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return parseFloat(b.rating?.toString() || '0') - parseFloat(a.rating?.toString() || '0');
        });
        break;
      case 'price-low':
        filtered.sort((a, b) => parseFloat(a.price?.toString() || '0') - parseFloat(b.price?.toString() || '0'));
        break;
      case 'price-high':
        filtered.sort((a, b) => parseFloat(b.price?.toString() || '0') - parseFloat(a.price?.toString() || '0'));
        break;
      case 'rating':
        filtered.sort((a, b) => parseFloat(b.rating?.toString() || '0') - parseFloat(a.rating?.toString() || '0'));
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      default:
        break;
    }

    return filtered;
  }, [allListings, searchQuery, selectedCategory, sortBy, priceRange]);

  // Get featured services
  const featuredServices = useMemo(() => {
    if (!allListings) return [];
    return allListings.filter(listing => listing.featured).slice(0, 8);
  }, [allListings]);

  // Paginated listings
  const paginatedListings = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredAndSortedListings.slice(startIndex, endIndex);
  }, [filteredAndSortedListings, currentPage, itemsPerPage]);

  // Total pages
  const totalPages = Math.ceil(filteredAndSortedListings.length / itemsPerPage);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory, sortBy, priceRange]);

  if (categoriesLoading || listingsLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-16 px-4 bg-gradient-primary text-white">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Professional Services
          </h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Connect with talented freelancers and agencies for your business needs
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-32 py-4 text-lg rounded-full border-none bg-white text-gray-900"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
              <Button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 whitespace-nowrap"
                onClick={() => {
                  document.getElementById('services-section')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Categories Grid */}
        <section className="mb-16">
          <div className="flex flex-wrap gap-4 justify-center">
            {categoriesWithCounts.filter(category => {
              const count = allListings?.filter(listing => listing.category_id === category.id).length || 0;
              return count > 0;
            }).map((category) => (
              <div
                key={category.id}
                className="flex items-center gap-2 px-4 py-2 bg-white rounded-full border hover:shadow-md transition-shadow cursor-pointer hover:border-primary"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon && <i className={`${category.icon} text-lg text-primary`}></i>}
                <span className="font-medium text-gray-700">{category.name}</span>
                <span className="text-sm text-gray-500">({category.count})</span>
              </div>
            ))}
          </div>
        </section>

        {/* Featured Services */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Popular Services</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredServices.map((service) => (
              <Link key={service.id} to={`/listing/${service.slug}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardHeader>
                    <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                      {service.image_url ? (
                        <img
                          src={service.image_url}
                          alt={service.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400">No Image</span>
                        </div>
                      )}
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{service.title}</CardTitle>
                    <CardDescription>
                      <Badge variant="outline" className="mb-2">
                        {service.categories?.name || 'Uncategorized'}
                      </Badge>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-2">
                        {service.description}
                      </p>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Starting at</span>
                        <span className="text-2xl font-bold text-primary">${service.price}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span>{parseFloat(service.rating?.toString() || '0').toFixed(1)} ({service.review_count})</span>
                        </div>
                        <span>by {service.seller_name}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* Filters and All Services */}
        <section id="services-section" className="mb-16">
          <div className="flex flex-col lg:flex-row gap-6 mb-8">
            <div className="flex-1">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">All Professional Services</h2>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories?.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Price:</span>
                <Select value={priceRange} onValueChange={setPriceRange}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="under-100">Under $100</SelectItem>
                    <SelectItem value="100-500">$100 - $500</SelectItem>
                    <SelectItem value="500-1000">$500 - $1000</SelectItem>
                    <SelectItem value="over-1000">Over $1000</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <SortAsc className="h-4 w-4" />
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured">Featured</SelectItem>
                    <SelectItem value="newest">Newest</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {paginatedListings.map((service) => (
              <Link key={service.id} to={`/listing/${service.slug}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardHeader>
                    <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                      {service.image_url ? (
                        <img
                          src={service.image_url}
                          alt={service.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400">No Image</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-start justify-between mb-2">
                      <CardTitle className="text-lg line-clamp-2 flex-1">{service.title}</CardTitle>
                      {service.featured && (
                        <Badge variant="secondary" className="ml-2 text-xs">Featured</Badge>
                      )}
                    </div>
                    <CardDescription>
                      <Badge variant="outline" className="mb-2">
                        {service.categories?.name || 'Uncategorized'}
                      </Badge>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-2">
                        {service.description}
                      </p>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Starting at</span>
                        <span className="text-xl font-bold text-primary">${service.price}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span>{parseFloat(service.rating?.toString() || '0').toFixed(1)}</span>
                        </div>
                        <div className="text-right">
                          <div>{service.review_count} reviews</div>
                          <div className="text-xs text-gray-400">by {service.seller_name}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mb-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(pageNum)}
                      className="w-10"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}

          {/* Results info */}
          <div className="text-center text-sm text-gray-500 mb-4">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredAndSortedListings.length)} of {filteredAndSortedListings.length} services
          </div>

          {filteredAndSortedListings.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No services found matching your criteria.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                  setPriceRange('all');
                  setSortBy('featured');
                  setCurrentPage(1);
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </section>

        {/* How It Works Section */}
        <section className="mt-16 bg-white rounded-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">How Services Work</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Browse & Choose</h3>
              <p className="text-gray-600">Find the perfect service provider for your project needs</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Collaborate</h3>
              <p className="text-gray-600">Work directly with freelancers through our secure platform</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Get Results</h3>
              <p className="text-gray-600">Receive high-quality work delivered on time and on budget</p>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Services;

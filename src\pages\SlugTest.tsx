import React, { useState } from 'react';
import { generateSlug, generateUniqueSlug } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const SlugTest = () => {
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [existingSlugs] = useState([
    'amazing-web-design-template',
    'react-dashboard-component',
    'seo-optimization-service'
  ]);

  const handleGenerateSlug = () => {
    const baseSlug = generateSlug(title);
    const uniqueSlug = generateUniqueSlug(baseSlug, existingSlugs);
    setSlug(uniqueSlug);
  };

  const testCases = [
    'Amazing Web Design Template!',
    'React Dashboard Component',
    'SEO Optimization Service',
    'E-commerce Store Builder 2024',
    'Mobile App Development & Design',
    'WordPress Theme - Premium Quality',
    'AI-Powered Content Generator',
    'Social Media Marketing Package'
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Slug Generator Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Product Title</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a product title..."
            />
          </div>
          
          <Button onClick={handleGenerateSlug} disabled={!title.trim()}>
            Generate Slug
          </Button>
          
          {slug && (
            <div>
              <label className="block text-sm font-medium mb-2">Generated Slug</label>
              <div className="p-3 bg-gray-100 rounded border">
                <code>{slug}</code>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                URL would be: <code>http://localhost:8081/product/{slug}</code>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Cases</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {testCases.map((testTitle, index) => {
              const baseSlug = generateSlug(testTitle);
              const uniqueSlug = generateUniqueSlug(baseSlug, existingSlugs);
              
              return (
                <div key={index} className="border-b pb-3">
                  <div className="font-medium">{testTitle}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    Slug: <code className="bg-gray-100 px-2 py-1 rounded">{uniqueSlug}</code>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    URL: <code>http://localhost:8081/product/{uniqueSlug}</code>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Existing Slugs (for uniqueness testing)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {existingSlugs.map((existingSlug, index) => (
              <div key={index} className="text-sm">
                <code className="bg-gray-100 px-2 py-1 rounded">{existingSlug}</code>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SlugTest;

import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from "@/components/layout/PageLayout";
import { DetailHero } from "@/components/detail/shared/DetailHero";
import { MediaGallery } from "@/components/detail/shared/MediaGallery";
import { ActionButtons } from "@/components/detail/shared/ActionButtons";
import { LicenseSelector } from "@/components/detail/tool/LicenseSelector";
import { DetailTabs } from "@/components/detail/shared/DetailTabs";
import { FeaturesList } from "@/components/detail/shared/FeaturesList";
import { TechSpecs } from "@/components/detail/tool/TechSpecs";
import { ToolHighlights } from "@/components/detail/tool/ToolHighlights";
import { ReviewsSection } from "@/components/detail/shared/ReviewsSection";
import { Button } from "@/components/ui/button";
import { ShoppingCart } from 'lucide-react';
import { useListing } from '@/hooks/useSupabaseData';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { StripeClient } from '@/integrations/stripe/client';
import { useAddToWishlist, useRemoveFromWishlist, useIsInWishlist } from '@/hooks/useSupabaseData';

// Inline useProfile hook
const useProfile = (id?: string) => {
  return useQuery({
    queryKey: ['profile', id],
    queryFn: async () => {
      if (!id) return null;
      const { data, error } = await supabase
        .from('profiles')
        .select('*, listings(*), reviews:reviews(*, profiles(*))')
        .eq('id', id)
        .single();
      if (error) throw error;
      return data;
    },
    enabled: !!id
  });
};

const ToolDetail = () => {
  const { identifier } = useParams<{ identifier: string }>();
  const { data: tool, isLoading } = useListing(identifier || '');
  const sellerId = tool?.seller_id;
  const { data: seller } = useProfile(sellerId);
  const sellerReviews = seller?.reviews || [];
  const totalSales = sellerReviews.length;
  const avgRating = sellerReviews.length ? sellerReviews.reduce((sum, r) => sum + (r.rating || 0), 0) / sellerReviews.length : 0;
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const addToWishlist = useAddToWishlist();
  const removeFromWishlist = useRemoveFromWishlist();
  const { data: isInWishlist } = useIsInWishlist(user?.id, tool?.id);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const handlePurchase = () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to purchase this tool.",
      });
      return;
    }
    setShowPurchaseModal(true);
  };

  const handleStripeCheckout = async () => {
    if (!user || !tool) {
      toast({
        title: "Error",
        description: "Please log in first",
      });
      return;
    }

    try {
      const baseUrl = window.location.origin;
      await StripeClient.initiatePurchase({
        price: tool.price || 0,
        productName: tool.title,
        productId: tool.id,
        buyerEmail: user.email || '',
        successUrl: `${baseUrl}/order-confirmation?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${baseUrl}/order-cancelled?product_id=${tool.id}`,
      });
    } catch (error) {
      console.error('Stripe checkout error:', error);
      toast({
        title: "Checkout Error",
        description: "Failed to initiate checkout. Please try again.",
      });
    }
  };

  const handleWishlist = async () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to add items to your wishlist.",
      });
      return;
    }

    if (!tool) return;

    try {
      if (isInWishlist) {
        await removeFromWishlist.mutateAsync({ userId: user.id, listingId: tool.id });
        toast({
          title: "Removed from Wishlist",
          description: "This tool has been removed from your wishlist.",
        });
      } else {
        await addToWishlist.mutateAsync({ userId: user.id, listingId: tool.id });
        toast({
          title: "Added to Wishlist",
          description: "This tool has been added to your wishlist.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
      });
    }
  };

  const handlePreview = () => {
    setShowPreviewModal(true);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: tool?.title || 'Tool',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link Copied",
        description: "Tool link copied to clipboard.",
      });
    }
  };

  const [buyerInfo, setBuyerInfo] = useState({
    fullName: user?.user_metadata?.full_name || '',
    email: user?.email || '',
    phone: '',
    address: '',
    notes: ''
  });

  const handlePurchaseSubmit = async () => {
    try {
      // Create order in database
      const orderData = {
        listing_id: tool?.id,
        buyer_id: user?.id,
        seller_id: tool?.seller_id,
        amount: tool?.price || 0,
        status: 'pending',
        notes: buyerInfo.notes,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('orders')
        .insert([orderData]);

      if (error) throw error;

      toast({
        title: "Order Created!",
        description: "Your order has been placed successfully.",
      });

      setShowPurchaseModal(false);
      // TODO: Redirect to payment or order confirmation
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create order. Please try again.",
      });
    }
  };

  if (isLoading) {
    return <div className="max-w-7xl mx-auto px-4 py-8">Loading...</div>;
  }

  if (!tool) {
    return <div className="max-w-7xl mx-auto px-4 py-8 font-bold">Tool not found.</div>;
  }

  const tabs = [
    {
      value: "description",
      label: "Description",
      content: tool?.description || ''
    },
    {
      value: "features",
      label: "Features",
      content: <FeaturesList features={tool?.features || []} />
    },
    {
      value: "included",
      label: "What's Included",
      content: <FeaturesList features={tool?.features || []} type="included" />
    },
    {
      value: "tech",
      label: "Tech Specs",
      content: (() => {
        const specs = typeof tool?.tech_specs === 'object' && tool?.tech_specs !== null && !Array.isArray(tool?.tech_specs)
          ? tool.tech_specs as Record<string, any>
          : {};
        return (
          <TechSpecs
            specs={{
              language: specs.language || "",
              database: specs.database || "",
              framework: specs.framework || "",
              frontend: specs.frontend || "",
              responsive: specs.responsive || "",
              browsers: specs.browsers || "",
            }}
          />
        );
      })()
    }
  ];

  return (
    <PageLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div className="space-y-6">
            <DetailHero
              category={tool.categories?.name || ''}
              title={tool.title}
              rating={tool.rating}
              reviews={tool.review_count}
              price={tool.price ? `$${tool.price}` : ''}
              tags={tool.tags || []}
            />

            <ActionButtons
              primaryAction={{
                label: "Buy Now",
                icon: <ShoppingCart className="w-5 h-5 mr-2" />
              }}
              onPrimaryAction={handleStripeCheckout}
              showPreview={true}
              showWishlist={true}
              showShare={true}
              isInWishlist={isInWishlist}
              onWishlist={handleWishlist}
              onPreview={handlePreview}
              onShare={handleShare}
            />
          </div>

          <MediaGallery images={tool.gallery_urls || []} title={tool.title} />
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <DetailTabs tabs={tabs} defaultValue="description" />
          </div>

          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">About the Creator</h3>
              <div className="flex items-center gap-4">
                <img src="/placeholder.svg" alt={seller?.full_name || 'Unknown'} className="w-12 h-12 rounded-full" />
                <div>
                  <h4 className="font-semibold">{seller?.full_name || 'Unknown Seller'}</h4>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>{avgRating.toFixed(1)} ★</span>
                    <span>•</span>
                    <span>{totalSales} Sales</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      <Dialog open={showPreviewModal} onOpenChange={setShowPreviewModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Preview: {tool?.title}</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {tool?.gallery_urls && tool.gallery_urls.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Images</h3>
                <div className="grid grid-cols-2 gap-4">
                  {tool.gallery_urls.slice(0, 4).map((url, index) => (
                    <img 
                      key={index} 
                      src={url} 
                      alt={`Preview ${index + 1}`} 
                      className="w-full h-48 object-cover rounded-lg"
                    />
                  ))}
                </div>
              </div>
            )}
            
            {tool?.description && (
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-gray-700 whitespace-pre-line">{tool.description}</p>
              </div>
            )}
            
            {tool?.features && tool.features.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Features</h3>
                <ul className="list-disc list-inside space-y-1">
                  {tool.features.map((feature, index) => (
                    <li key={index} className="text-gray-700">{feature}</li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="flex justify-end">
              <Button onClick={() => setShowPreviewModal(false)}>
                Close Preview
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showPurchaseModal} onOpenChange={setShowPurchaseModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Complete Purchase</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                value={buyerInfo.fullName}
                onChange={(e) => setBuyerInfo(prev => ({ ...prev, fullName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={buyerInfo.email}
                onChange={(e) => setBuyerInfo(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={buyerInfo.phone}
                onChange={(e) => setBuyerInfo(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="address">Address</Label>
              <Textarea
                id="address"
                value={buyerInfo.address}
                onChange={(e) => setBuyerInfo(prev => ({ ...prev, address: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={buyerInfo.notes}
                onChange={(e) => setBuyerInfo(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>
            <div className="flex justify-between items-center pt-4">
              <div>
                <p className="font-semibold">Total: ${tool?.price}</p>
              </div>
              <div className="space-x-2">
                <Button variant="outline" onClick={() => setShowPurchaseModal(false)}>
                  Cancel
                </Button>
                <Button onClick={handlePurchaseSubmit}>
                  Complete Purchase
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </PageLayout>
  );
};

export default ToolDetail;
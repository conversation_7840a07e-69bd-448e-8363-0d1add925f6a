import React, { useState } from 'react';
import { ProductLayout } from '@/components/detail/shared/ProductLayout';
import { ProductHeader } from '@/components/detail/shared/ProductHeader';
import { MediaGallery } from '@/components/detail/shared/MediaGallery';
import { PricingSection } from '@/components/detail/shared/PricingSection';
import { SellerInfo } from '@/components/detail/shared/SellerInfo';
import { ProductInfo } from '@/components/detail/shared/ProductInfo';
import { ToolLicenses } from '@/components/detail/tool/ToolLicenses';
import { useProductDetail } from '@/hooks/useProductDetail';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Zap, Shield, Code, Globe, CheckCircle } from 'lucide-react';

const ToolDetail = () => {
  const {
    product: tool,
    reviews,
    seller,
    isLoading,
    avgRating,
    totalSales,
    isProductOwner,
    hasPurchased,
    isInWishlist,
    buyNowLoading,
    showPreviewModal,
    setShowPreviewModal,
    handleStripeCheckout,
    handleWishlist,
    handlePreview,
    handleShare,
    handleMessageSent,
  } = useProductDetail();

  const [selectedLicense, setSelectedLicense] = useState<string>('standard');

  const handleSelectLicense = (licenseId: string, price: number) => {
    setSelectedLicense(licenseId);
    // Update the tool price if needed
  };

  const handleGetTool = () => {
    handleStripeCheckout();
  };

  if (isLoading) {
    return (
      <ProductLayout product={{ type: 'tool', title: 'Loading...' }}>
        <div className="animate-pulse">
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="aspect-video bg-gray-200 rounded-lg mb-6"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            </div>
            <div className="space-y-6">
              <div className="h-32 bg-gray-200 rounded-lg"></div>
              <div className="h-48 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </ProductLayout>
    );
  }

  if (!tool) {
    return (
      <ProductLayout product={{ type: 'tool', title: 'Not Found' }}>
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Tool Not Found</h1>
          <p className="text-gray-600 mb-8">The tool you're looking for doesn't exist or has been removed.</p>
          <Link to="/micro-saas" className="text-blue-600 hover:text-blue-800 font-medium">
            ← Back to Micro SaaS
          </Link>
        </div>
      </ProductLayout>
    );
  }

  return (
    <ProductLayout product={tool}>
      {/* Main Tool Section */}
      <div className="grid lg:grid-cols-3 gap-8 mb-12">
        {/* Left Column - Screenshots Gallery */}
        <div className="lg:col-span-2">
          <MediaGallery 
            images={tool.gallery_urls || []} 
            title={tool.title}
            mainImage={tool.image_url}
            productType="tool"
          />
        </div>
        
        {/* Right Column - Tool Info & Pricing */}
        <div className="space-y-6">
          <ProductHeader 
            product={tool}
            avgRating={avgRating}
            reviewCount={reviews.length}
          />

          {/* Tool Highlights */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-purple-600" />
                <span className="text-purple-800">Instant Access</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-purple-600" />
                <span className="text-purple-800">Secure & Reliable</span>
              </div>
              <div className="flex items-center gap-2">
                <Code className="w-4 h-4 text-purple-600" />
                <span className="text-purple-800">API Available</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-purple-600" />
                <span className="text-purple-800">Cloud-based</span>
              </div>
            </div>
          </div>

          <PricingSection
            product={tool}
            onPurchase={handleGetTool}
            loading={buyNowLoading}
            isOwner={isProductOwner}
            hasPurchased={hasPurchased}
          />

          {/* Seller Info */}
          {!isProductOwner && seller && (
            <SellerInfo
              seller={{
                id: seller.id,
                name: seller.full_name || tool.seller_name,
                rating: avgRating,
                sales: totalSales,
                responseTime: '< 4 hours',
                memberSince: new Date(seller.created_at).getFullYear().toString()
              }}
              onContact={handleMessageSent}
              onFollow={() => {}}
            />
          )}
        </div>
      </div>

      {/* Tool Details Section */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Description, Licenses, and Features */}
        <div className="lg:col-span-2 space-y-8">
          {/* Description */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">About This Tool</h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {tool.description}
              </p>
            </div>
          </div>

          {/* License Options */}
          <ToolLicenses
            tool={tool}
            onSelectLicense={handleSelectLicense}
          />

          {/* Features */}
          {tool.features && tool.features.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid md:grid-cols-2 gap-3">
                {tool.features.map((feature: string, index: number) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                      <CheckCircle className="w-3 h-3 text-purple-600" />
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Technical Specifications */}
          {tool.tech_specs && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Specifications</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-4">
                  {Object.entries(tool.tech_specs).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
                      <span className="text-gray-900 font-medium">{value as string}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Getting Started */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Getting Started</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Purchase & Setup</h3>
                  <p className="text-gray-600 text-sm">Complete your purchase and receive instant access to your dashboard.</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Configure Settings</h3>
                  <p className="text-gray-600 text-sm">Customize the tool settings to match your specific needs and preferences.</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Start Using</h3>
                  <p className="text-gray-600 text-sm">Begin using the tool immediately with full access to all features and support.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          {reviews.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">User Reviews</h2>
              <div className="space-y-6">
                {reviews.slice(0, 5).map((review: any) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {(review.buyer_name || 'A')[0].toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-semibold text-gray-900">{review.buyer_name}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <svg
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(review.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Additional Info */}
        <div className="space-y-6">
          <ProductInfo product={tool} />
        </div>
      </div>
    </ProductLayout>
  );
};

export default ToolDetail;

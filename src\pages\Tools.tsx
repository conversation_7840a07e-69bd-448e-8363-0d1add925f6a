import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PageLayout } from "@/components/layout/PageLayout";

const Tools = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    {
      title: "Productivity Tools",
      description: "Task management, scheduling, and workflow tools",
      count: "320+ tools"
    },
    {
      title: "Analytics Tools",
      description: "Data analysis, tracking, and reporting solutions",
      count: "180+ tools"
    },
    {
      title: "Design Tools",
      description: "Creative software, templates, and design assets",
      count: "450+ tools"
    },
    {
      title: "Developer Tools",
      description: "Code utilities, APIs, and development resources",
      count: "290+ tools"
    },
    {
      title: "Marketing Tools",
      description: "Email marketing, social media, and automation tools",
      count: "240+ tools"
    },
    {
      title: "Business Tools",
      description: "CRM, invoicing, and business management solutions",
      count: "200+ tools"
    }
  ];

  const featuredTools = [
    {
      title: "Advanced Analytics Dashboard",
      startingPrice: "$49",
      rating: 4.9,
      reviews: 124,
      category: "Analytics Tools",
      delivery: "Instant"
    },
    {
      title: "Project Management Suite",
      startingPrice: "$29",
      rating: 4.8,
      reviews: 198,
      category: "Productivity Tools",
      delivery: "Instant"
    },
    {
      title: "Social Media Automation Tool",
      startingPrice: "$39",
      rating: 4.7,
      reviews: 156,
      category: "Marketing Tools",
      delivery: "Instant"
    },
    {
      title: "Code Generation Toolkit",
      startingPrice: "$59",
      rating: 4.9,
      reviews: 89,
      category: "Developer Tools",
      delivery: "Instant"
    }
  ];

  return (
    <PageLayout>
      <section className="py-16 px-4 bg-gradient-primary text-white">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Digital Tools & Software
          </h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Powerful tools and software solutions to boost your productivity
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search tools and software..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-32 py-4 text-lg rounded-full border-none bg-white text-gray-900"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
              <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 whitespace-nowrap">
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-6xl mx-auto px-4 py-16">
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Tool Categories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary">{category.count}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Tools</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredTools.map((tool, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
                  <CardTitle className="text-lg line-clamp-2">{tool.title}</CardTitle>
                  <CardDescription>
                    <Badge variant="outline" className="mb-2">{tool.category}</Badge>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Starting at</span>
                      <span className="text-2xl font-bold text-primary">{tool.startingPrice}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        {tool.rating} ({tool.reviews})
                      </div>
                      <span>{tool.delivery}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Tools;

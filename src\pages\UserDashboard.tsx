import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  ShoppingBag, 
  Download, 
  Settings, 
  MessageSquare, 
  Star, 
  Heart,
  Search
} from "lucide-react";
import { DashboardHeader } from "@/components/dashboard/shared/DashboardHeader";
import { PurchaseHistory } from "@/components/dashboard/user/PurchaseHistory";
import { ServicesList } from "@/components/dashboard/user/ServicesList";
import { MessagesList } from "@/components/dashboard/user/MessagesList";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useUserOrders, useUserDownloads } from '@/hooks/useSupabaseData';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import DownloadsList from '@/components/dashboard/user/DownloadsList';

const UserDashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { user } = useAuth();
  const { data: orders = [], isLoading: ordersLoading } = useUserOrders(user?.id);
  const { data: downloads = [], isLoading: downloadsLoading } = useUserDownloads(user?.id);
  const [messages, setMessages] = useState<any[]>([]);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [totalDownloads, setTotalDownloads] = useState(0);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const defaultTab = searchParams.get('tab') || 'purchases';

  useEffect(() => {
    if (!user) return;
    
    const fetchDashboardStats = async () => {
      // Fetch messages
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .order('created_at', { ascending: false });
      if (!messagesError && messagesData) setMessages(messagesData);

      // Fetch wishlist count
      const { data: wishlistData, error: wishlistError } = await supabase
        .from('wishlist')
        .select('id', { count: 'exact' })
        .eq('user_id', user.id);
      if (!wishlistError) setWishlistCount(wishlistData?.length || 0);

      // Fetch total downloads count
      const { data: downloadsData, error: downloadsError } = await supabase
        .from('downloads')
        .select('download_count')
        .in('order_id', orders.map(order => order.id));
      if (!downloadsError && downloadsData) {
        const total = downloadsData.reduce((sum, download) => sum + (download.download_count || 0), 0);
        setTotalDownloads(total);
      }
    };

    fetchDashboardStats();
  }, [user, orders]);

  // Refresh messages when returning to dashboard
  useEffect(() => {
    const handleFocus = () => {
      if (user) {
        const fetchMessages = async () => {
          const { data, error } = await supabase
            .from('messages')
            .select('*')
            .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
            .order('created_at', { ascending: false });
          if (!error && data) setMessages(data);
        };
        fetchMessages();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [user]);

  useEffect(() => {
    const handleStorage = (e: StorageEvent) => {
      if (e.key === 'refreshMessages') {
        if (user) {
          const fetchMessages = async () => {
            const { data, error } = await supabase
              .from('messages')
              .select('*')
              .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
              .order('created_at', { ascending: false });
            if (!error && data) setMessages(data);
          };
          fetchMessages();
        }
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, [user]);

  const purchaseHistory = orders.map(order => ({
    id: Number(order.id),
    title: order.listings?.title || 'Unknown Product',
    seller: order.profiles?.full_name || 'Unknown Seller',
    price: `$${order.amount}`,
    date: order.created_at ? new Date(order.created_at).toLocaleDateString() : '',
    status: order.status,
    downloadUrl: order.download_urls && order.download_urls.length > 0 ? order.download_urls[0] : null
  }));

  // Map downloads to expected Download[] type
  const mappedDownloads = downloads.map(d => ({
    id: Number(d.id),
    title: d.file_name,
    type: d.file_type,
    size: d.file_size ? `${d.file_size} MB` : '',
    downloadCount: d.download_count,
    lastDownload: d.last_download || '',
    downloadUrl: d.download_url
  }));

  // Use empty arrays for services if not available
  const mappedServices = [];

  // Only show messages where the user is the recipient (inbox) or sender (sent) by user_id
  const userId = user?.id;
  const filteredMessages = messages.filter(
    (msg, idx, arr) =>
      (msg.sender_id === userId || msg.recipient_id === userId) &&
      arr.findIndex(m => m.id === msg.id) === idx
  );

  // Conversation grouping for sidebar
  const conversations = React.useMemo(() => {
    const map = new Map();
    filteredMessages.forEach((msg) => {
      const otherId = msg.sender_id === userId ? msg.recipient_id : msg.sender_id;
      if (!map.has(otherId)) map.set(otherId, []);
      map.get(otherId).push(msg);
    });
    return Array.from(map.entries()).map(([otherId, msgs]) => {
      const lastMsg = msgs[0];
      // Only count messages that are actually unread (read: false or null)
      const unreadCount = msgs.filter(msg => 
        msg.recipient_id === userId && (msg.read === false || msg.read === null)
      ).length;
      return {
        otherId,
        otherName: lastMsg.sender_id === userId ? lastMsg.recipient_name : lastMsg.sender_name,
        otherAvatar: '/placeholder.svg',
        lastSubject: lastMsg.subject,
        lastDate: lastMsg.created_at,
        unreadCount,
        hasUnread: unreadCount > 0,
      };
    });
  }, [filteredMessages, userId]);

  const wishlist = [
    {
      id: 1,
      title: "Advanced React Course",
      seller: "CodeAcademy",
      price: "$199",
      rating: 4.8,
      reviews: 234
    },
    {
      id: 2,
      title: "Premium Icon Pack",
      seller: "IconDesigner",
      price: "$49",
      rating: 4.9,
      reviews: 156
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader 
        title="My Dashboard"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <ShoppingBag className="w-8 h-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Purchases</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {ordersLoading ? '...' : orders.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Download className="w-8 h-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Downloads</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {downloadsLoading ? '...' : totalDownloads}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MessageSquare className="w-8 h-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Messages</p>
                  <p className="text-2xl font-bold text-gray-900">{messages.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Heart className="w-8 h-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Wishlist</p>
                  <p className="text-2xl font-bold text-gray-900">{wishlistCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue={defaultTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="purchases" className="flex items-center gap-2">
              <ShoppingBag className="w-4 h-4" />
              Purchases
            </TabsTrigger>
            <TabsTrigger value="downloads" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Downloads
            </TabsTrigger>
            <TabsTrigger value="services" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Services
            </TabsTrigger>
            <TabsTrigger value="messages" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Messages
            </TabsTrigger>
            <TabsTrigger value="reviews" className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              Reviews
            </TabsTrigger>
            <TabsTrigger value="wishlist" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Wishlist
            </TabsTrigger>
          </TabsList>

          <TabsContent value="purchases" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Purchase History</h2>
              <div className="relative w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search purchases..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <PurchaseHistory purchases={purchaseHistory} />
          </TabsContent>

          <TabsContent value="downloads" className="space-y-6">
            <h2 className="text-2xl font-semibold">Downloads</h2>
            <DownloadsList />
          </TabsContent>

          <TabsContent value="services" className="space-y-6">
            <h2 className="text-2xl font-semibold">Active Services</h2>
            <ServicesList services={mappedServices} />
          </TabsContent>

          <TabsContent value="messages" className="space-y-6">
            <h2 className="text-2xl font-semibold">Messages</h2>
            <div className="space-y-2">
              {conversations.length === 0 && <div className="text-gray-500 text-center py-8">No conversations yet.</div>}
              {conversations.map(convo => (
                <div key={convo.otherId} className="flex items-center gap-3 p-3 border rounded cursor-pointer hover:bg-gray-50" onClick={() => navigate(`/messages/${convo.otherId}`)}>
                  <Avatar><AvatarFallback>{convo.otherName[0]}</AvatarFallback></Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <div className={`font-semibold ${convo.hasUnread ? 'font-bold' : ''}`}>{convo.otherName}</div>
                      {convo.hasUnread && (
                        <div className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {convo.unreadCount}
                        </div>
                      )}
                    </div>
                    <div className={`text-sm truncate ${convo.hasUnread ? 'font-medium text-gray-900' : 'text-gray-600'}`}>{convo.lastSubject}</div>
                  </div>
                  <div className="text-xs text-gray-400">{new Date(convo.lastDate).toLocaleDateString()}</div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-6">
            <h2 className="text-2xl font-semibold">My Reviews</h2>
            <div className="text-center py-12">
              <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No reviews yet</p>
              <p className="text-sm text-gray-500 mt-2">
                Reviews you write will appear here
              </p>
            </div>
          </TabsContent>

          <TabsContent value="wishlist" className="space-y-6">
            <h2 className="text-2xl font-semibold">Wishlist</h2>
            <div className="grid gap-4">
              {wishlist.map((item) => (
                <Card key={item.id}>
                  <CardContent className="flex items-center justify-between p-6">
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>{item.seller[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{item.title}</h3>
                        <p className="text-sm text-gray-600">by {item.seller}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <div className="flex">{renderStars(item.rating)}</div>
                          <span className="text-sm text-gray-600">({item.reviews})</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-lg font-bold">{item.price}</span>
                      <Button size="sm">Add to Cart</Button>
                      <Button variant="outline" size="sm">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UserDashboard;

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// @ts-ignore
const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY");
const STRIPE_API = "https://api.stripe.com/v1/checkout/sessions";

// Common headers for all responses
const headers = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, apikey, x-client-info",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

serve(async (req) => {
  // Preflight request for CORS
  if (req.method === "OPTIONS") {
    return new Response(null, { status: 204, headers });
  }

  // Only allow POST
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers,
    });
  }

  try {
    // Parse request body
    const { price, productName, productId, buyerEmail, successUrl, cancelUrl, buyerId } = await req.json();

    // Validate required fields
    if (!STRIPE_SECRET_KEY || !price || !productName || !productId || !buyerEmail || !successUrl || !cancelUrl) {
      return new Response(JSON.stringify({ error: "Missing required fields" }), {
        status: 400,
        headers,
      });
    }

    // Build Stripe parameters
    const params = new URLSearchParams();
    params.append("mode", "payment");
    params.append("success_url", successUrl);
    params.append("cancel_url", cancelUrl);
    params.append("customer_email", buyerEmail);
    params.append("client_reference_id", productId);
    params.append("metadata[buyer_id]", buyerId);
    params.append("line_items[0][price_data][currency]", "usd");
    params.append("line_items[0][price_data][unit_amount]", Math.round(Number(price) * 100).toString());
    params.append("line_items[0][price_data][product_data][name]", productName);
    params.append("line_items[0][quantity]", "1");

    // Call Stripe API
    const stripeRes = await fetch(STRIPE_API, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${STRIPE_SECRET_KEY}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: params,
    });

    const data = await stripeRes.json();

    if (!stripeRes.ok) {
      return new Response(JSON.stringify({ error: data.error?.message || "Stripe error" }), {
        status: 500,
        headers,
      });
    }

    // Success: return checkout URL and session ID
    return new Response(JSON.stringify({ url: data.url, id: data.id }), {
      status: 200,
      headers,
    });
  } catch (err) {
    return new Response(JSON.stringify({ error: err.message || "Unknown error" }), {
      status: 500,
      headers,
    });
  }
});

// supabase/functions/stripeWebhook/index.ts
// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
// @ts-ignore
import <PERSON><PERSON> from "https://esm.sh/stripe@14.20.0?target=deno";
// @ts-ignore
const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY");
// @ts-ignore
const STRIPE_WEBHOOK_SECRET = Deno.env.get("STRIPE_WEBHOOK_SECRET");
// @ts-ignore
const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
// @ts-ignore
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16"
});
serve(async (req)=>{
  if (req.method !== "POST") {
    return new Response("Method Not Allowed", {
      status: 405
    });
  }
  const body = await req.text();
  const sig = req.headers.get("stripe-signature");
  let event;
  // Verify signature
  try {
    event = await stripe.webhooks.constructEventAsync(body, sig, STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error("⚠️ Webhook signature verification failed:", err.message);
    return new Response("Invalid signature", {
      status: 400
    });
  }
  try {
    if (event.type === "checkout.session.completed") {
      console.log(JSON.stringify(event));
      const session = event.data.object;
      const productId = session.client_reference_id;
      const buyerEmail = session.customer_email;
      const buyerName = session.customer_details.name;
      const buyerId = session.metadata.buyer_id;
      const amount = session.amount_total / 100;
      const stripeSessionId = session.id;
      const stripePaymentIntentId = session.payment_intent;
      // Fetch the listing to get the correct seller_name and seller_id
      const { data: listing, error: listingError } = await supabase.from("listings").select("seller_name, seller_id").eq("id", productId).single();
      if (listingError || !listing) {
        console.error("Could not fetch listing for order:", listingError);
        return new Response("Listing not found", {
          status: 400
        });
      }
      // Insert or update order
      const { data: orderInsertData, error: orderInsertError } = await supabase.from("orders").insert([
        {
          listing_id: productId,
          buyer_id: buyerId,
          seller_id: listing.seller_id || null,
          buyer_email: buyerEmail || "<EMAIL>",
          amount: amount,
          status: "completed",
          stripe_session_id: stripeSessionId,
          stripe_payment_intent_id: stripePaymentIntentId,
          buyer_name: buyerName || "<EMAIL>",
          seller_name: listing.seller_name
        }
      ]).select();
      if (orderInsertError) {
        console.error("Supabase order insert error:", orderInsertError);
        return new Response("Supabase error", {
          status: 500
        });
      }
      const newOrder = orderInsertData && orderInsertData[0];
      if (!newOrder) {
        console.error("Order insert did not return new order");
        return new Response("Order insert error", { status: 500 });
      }
      // Fetch digital product files for the listing (original seller uploads)
      // These are the files uploaded by the seller when creating the listing
      const { data: digitalFiles, error: digitalFilesError } = await supabase
        .from("downloads")
        .select("file_name, file_type, file_size, download_url")
        .eq("listing_id", productId)
        .is("order_id", null);
      if (digitalFilesError) {
        console.error("Could not fetch digital product files for listing:", digitalFilesError);
      } else {
        console.log(`Found ${digitalFiles?.length || 0} digital files for listing ${productId}`);
        if (digitalFiles && digitalFiles.length > 0) {
          // Insert a downloads row for each file for the buyer
          for (const file of digitalFiles) {
            const { error: insertError } = await supabase.from("downloads").insert({
              order_id: newOrder.id,
              listing_id: productId,
              user_name: buyerName || buyerEmail,
              file_name: file.file_name,
              file_type: file.file_type,
              file_size: file.file_size,
              download_url: file.download_url,
              download_count: 0,
            });
            if (insertError) {
              console.error("Error inserting download record:", insertError);
            } else {
              console.log(`✅ Created download record for ${file.file_name}`);
            }
          }
        } else {
          console.log("No digital files found for this listing");
        }
      }
      console.log(`✅ Order created for ${buyerEmail} - $${amount}`);
    }
    return new Response("ok", {
      status: 200
    });
  } catch (err) {
    console.error("Webhook error:", err);
    return new Response("Webhook error", {
      status: 500
    });
  }
});

-- Profiles: user_id
create policy "Users can view their own profile" on public.profiles for select using (auth.uid() = user_id);
create policy "Users can update their own profile" on public.profiles for update using (auth.uid() = user_id);
create policy "Users can insert their own profile" on public.profiles for insert with check (auth.uid() = user_id);

-- Listings: seller_name (string, demo data)
create policy "Sellers can view their own listings" on public.listings for select using (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Sellers can update their own listings" on public.listings for update using (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Sellers can insert their own listings" on public.listings for insert with check (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Sellers can delete their own listings" on public.listings for delete using (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');

-- Orders: buyer_name and seller_name (string, demo data)
create policy "Buyers can view their own orders" on public.orders for select using (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Sellers can view their own orders" on public.orders for select using (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Buyers can insert their own orders" on public.orders for insert with check (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Sellers can update their own orders" on public.orders for update using (seller_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');

-- Reviews: buyer_name (string, demo data)
create policy "Buyers can view their own reviews" on public.reviews for select using (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Buyers can insert their own reviews" on public.reviews for insert with check (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Buyers can update their own reviews" on public.reviews for update using (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name');
create policy "Buyers can delete their own reviews" on public.reviews for delete using (buyer_name = auth.jwt() ->> 'user_metadata' ->> 'full_name'); 

-- Enable RLS on reviews table
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Policy: Allow insert if user is the buyer (by name match)
CREATE POLICY "Allow buyers to insert reviews" ON reviews
FOR INSERT
USING (
  EXISTS (
    SELECT 1 FROM orders
    WHERE orders.listing_id = reviews.listing_id
      AND orders.buyer_name = auth.jwt()->>'email'
      AND orders.status = 'completed'
  )
); 
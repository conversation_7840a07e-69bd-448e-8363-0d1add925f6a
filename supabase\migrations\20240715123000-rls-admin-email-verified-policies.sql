-- Admin access: allow all actions for admin role
create policy "<PERSON><PERSON> can do anything on profiles" on public.profiles for all using (auth.jwt() ->> 'role' = 'admin');
create policy "Ad<PERSON> can do anything on listings" on public.listings for all using (auth.jwt() ->> 'role' = 'admin');
create policy "Ad<PERSON> can do anything on orders" on public.orders for all using (auth.jwt() ->> 'role' = 'admin');
create policy "Ad<PERSON> can do anything on reviews" on public.reviews for all using (auth.jwt() ->> 'role' = 'admin');

-- Require email verification for all actions
create policy "Only verified emails can access profiles" on public.profiles for all using (auth.jwt() ->> 'email_verified' = 'true');
create policy "Only verified emails can access listings" on public.listings for all using (auth.jwt() ->> 'email_verified' = 'true');
create policy "Only verified emails can access orders" on public.orders for all using (auth.jwt() ->> 'email_verified' = 'true');
create policy "Only verified emails can access reviews" on public.reviews for all using (auth.jwt() ->> 'email_verified' = 'true'); 
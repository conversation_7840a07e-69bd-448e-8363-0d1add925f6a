-- Add UPDATE policy for messages table
-- Users can update messages where they are the recipient (to mark as read)

CREATE POLICY "Users can update messages they received" ON public.messages 
FOR UPDATE 
USING (
  recipient_id = auth.uid() OR 
  recipient_name = auth.jwt() ->> 'user_metadata' ->> 'full_name'
);

-- Also add INSERT policy for sending messages
CREATE POLICY "Users can insert messages" ON public.messages 
FOR INSERT 
WITH CHECK (
  sender_id = auth.uid() OR 
  sender_name = auth.jwt() ->> 'user_metadata' ->> 'full_name'
); 
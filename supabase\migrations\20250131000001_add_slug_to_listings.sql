-- Add slug column to listings table
ALTER TABLE public.listings ADD COLUMN slug text;

-- Create unique index on slug (allowing nulls for now)
CREATE UNIQUE INDEX listings_slug_unique ON public.listings(slug) WHERE slug IS NOT NULL;

-- Function to generate slug from title
CREATE OR REPLACE FUNCTION generate_slug_from_title(title_text text)
RETURNS text AS $$
BEGIN
  RETURN lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(trim(title_text), '[^\w\s-]', '', 'g'),
        '[\s_-]+', '-', 'g'
      ),
      '^-+|-+$', '', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique slug
CREATE OR REPLACE FUNCTION generate_unique_slug(base_slug text)
RETURNS text AS $$
DECLARE
  unique_slug text;
  counter integer := 1;
BEGIN
  unique_slug := base_slug;
  
  -- Check if slug already exists
  WHILE EXISTS (SELECT 1 FROM public.listings WHERE slug = unique_slug) LOOP
    unique_slug := base_slug || '-' || counter;
    counter := counter + 1;
  END LOOP;
  
  RETURN unique_slug;
END;
$$ LANGUAGE plpgsql;

-- Update existing listings with slugs based on their titles
UPDATE public.listings 
SET slug = generate_unique_slug(
  substring(generate_slug_from_title(title), 1, 100)
)
WHERE slug IS NULL;

-- Make slug column NOT NULL after populating existing records
ALTER TABLE public.listings ALTER COLUMN slug SET NOT NULL;

-- Create trigger to automatically generate slug on insert/update
CREATE OR REPLACE FUNCTION set_listing_slug()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate slug if it's not provided or if title changed
  IF NEW.slug IS NULL OR (TG_OP = 'UPDATE' AND OLD.title != NEW.title AND NEW.slug = OLD.slug) THEN
    NEW.slug := generate_unique_slug(
      substring(generate_slug_from_title(NEW.title), 1, 100)
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_set_listing_slug ON public.listings;
CREATE TRIGGER trigger_set_listing_slug
  BEFORE INSERT OR UPDATE ON public.listings
  FOR EACH ROW
  EXECUTE FUNCTION set_listing_slug();

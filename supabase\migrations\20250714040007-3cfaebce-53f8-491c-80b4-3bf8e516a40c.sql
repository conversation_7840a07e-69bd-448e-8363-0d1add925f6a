-- Create categories table
CREATE TABLE public.categories (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  slug text NOT NULL UNIQUE,
  description text,
  icon text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create listings table (for products and services)
CREATE TABLE public.listings (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text,
  type text NOT NULL CHECK (type IN ('product', 'service', 'tool')),
  price decimal(10,2),
  pricing_type text DEFAULT 'fixed' CHECK (pricing_type IN ('fixed', 'hourly', 'monthly', 'package')),
  image_url text,
  gallery_urls text[],
  category_id uuid REFERENCES public.categories(id),
  seller_name text NOT NULL, -- We'll use seller name instead of foreign key for demo data
  rating decimal(3,2) DEFAULT 0,
  review_count integer DEFAULT 0,
  featured boolean DEFAULT false,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'suspended')),
  tags text[],
  delivery_time integer, -- in days
  revisions integer,
  features text[],
  tech_specs jsonb,
  license_type text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create reviews table
CREATE TABLE public.reviews (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) ON DELETE CASCADE NOT NULL,
  buyer_name text NOT NULL, -- Using name for demo data
  rating integer CHECK (rating >= 1 AND rating <= 5) NOT NULL,
  comment text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create orders table  
CREATE TABLE public.orders (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  listing_id uuid REFERENCES public.listings(id) NOT NULL,
  buyer_name text NOT NULL,
  seller_name text NOT NULL,
  amount decimal(10,2) NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'cancelled', 'refunded', 'active')),
  delivery_date timestamp with time zone,
  download_urls text[],
  notes text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create messages table
CREATE TABLE public.messages (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id uuid REFERENCES public.orders(id),
  sender_name text NOT NULL,
  recipient_name text NOT NULL,
  subject text,
  content text NOT NULL,
  read boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create downloads table
CREATE TABLE public.downloads (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id uuid REFERENCES public.orders(id) NOT NULL,
  user_name text NOT NULL,
  file_name text NOT NULL,
  file_type text NOT NULL,
  file_size bigint NOT NULL,
  download_url text NOT NULL,
  download_count integer DEFAULT 0,
  last_download timestamp with time zone,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Enable RLS
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.downloads ENABLE ROW LEVEL SECURITY;

-- Categories policies (public read)
CREATE POLICY "Categories are viewable by everyone" ON public.categories FOR SELECT USING (true);

-- Listings policies
CREATE POLICY "Listings are viewable by everyone" ON public.listings FOR SELECT USING (status = 'active');

-- Reviews policies  
CREATE POLICY "Reviews are viewable by everyone" ON public.reviews FOR SELECT USING (true);

-- Orders policies
CREATE POLICY "Orders are viewable by everyone" ON public.orders FOR SELECT USING (true);

-- Messages policies
CREATE POLICY "Messages are viewable by everyone" ON public.messages FOR SELECT USING (true);

-- Downloads policies
CREATE POLICY "Downloads are viewable by everyone" ON public.downloads FOR SELECT USING (true);

-- Create updated_at triggers
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON public.categories FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_listings_updated_at BEFORE UPDATE ON public.listings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON public.reviews FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample categories
INSERT INTO public.categories (name, slug, description, icon) VALUES
('Design', 'design', 'Creative design services and digital assets', 'fas fa-palette'),
('Development', 'development', 'Web and mobile development services', 'fas fa-code'),
('Marketing', 'marketing', 'Digital marketing and promotion services', 'fas fa-bullhorn'),
('Tools', 'tools', 'Software tools and productivity solutions', 'fas fa-tools'),
('Digital Products', 'digital-products', 'Ready-to-use digital assets and templates', 'fas fa-download');

-- Insert sample listings based on the mock data
WITH category_ids AS (
  SELECT id, slug FROM public.categories
)
INSERT INTO public.listings (title, description, type, price, image_url, category_id, seller_name, rating, review_count, featured, tags) VALUES

-- Top Rated Listings
('Complete Brand Identity Package', 'Professional branding materials including logo, business cards, and letterhead', 'product', 299.00, 'https://readdy.ai/api/search-image?query=complete%20brand%20identity%20package%20with%20logo%20business%20cards%20letterhead%20mockups%20professional%20branding%20materials%20clean%20white%20background%20modern%20design&width=300&height=200&seq=toprated1&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'design'), 'DesignPro', 4.9, 234, true, ARRAY['branding', 'logo', 'identity']),

('Full-Stack Web Development', 'Complete web application development with modern technologies', 'service', 1299.00, 'https://readdy.ai/api/search-image?query=full%20stack%20web%20development%20with%20modern%20code%20editor%20multiple%20screens%20responsive%20design%20professional%20developer%20workspace%20clean%20setup&width=300&height=200&seq=toprated2&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'development'), 'CodeMaster', 4.8, 156, true, ARRAY['web-development', 'full-stack', 'react']),

('Advanced Analytics Dashboard', 'Custom analytics dashboard with real-time data visualization', 'tool', 149.00, 'https://readdy.ai/api/search-image?query=advanced%20analytics%20dashboard%20with%20charts%20graphs%20data%20visualization%20modern%20interface%20professional%20business%20intelligence%20clean%20design&width=300&height=200&seq=toprated3&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'tools'), 'DataViz', 4.8, 189, true, ARRAY['analytics', 'dashboard', 'data-viz']),

-- Trending Listings  
('Modern Website Template', 'Responsive website template with clean modern design', 'product', 89.00, 'https://readdy.ai/api/search-image?query=modern%20website%20template%20with%20clean%20responsive%20design%20multiple%20device%20mockups%20professional%20web%20layout%20minimalist%20interface&width=300&height=200&seq=trending1&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'design'), 'UIExpert', 4.7, 123, true, ARRAY['template', 'website', 'responsive']),

('Social Media Marketing Package', 'Complete social media marketing strategy and content creation', 'service', 399.00, 'https://readdy.ai/api/search-image?query=social%20media%20marketing%20package%20with%20content%20calendar%20post%20designs%20analytics%20professional%20marketing%20materials%20clean%20layout&width=300&height=200&seq=trending2&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'marketing'), 'MarketingPro', 4.6, 89, true, ARRAY['social-media', 'marketing', 'content']),

('E-commerce Mobile App', 'Native mobile app for e-commerce with payment integration', 'service', 2499.00, 'https://readdy.ai/api/search-image?query=e-commerce%20mobile%20app%20with%20shopping%20interface%20product%20listings%20payment%20screens%20modern%20UI%20professional%20app%20design&width=300&height=200&seq=trending3&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'development'), 'CodeMaster', 4.9, 67, true, ARRAY['mobile-app', 'e-commerce', 'payment']),

-- Recent Listings
('Mobile App UI Kit', 'Modern interface elements and clean layouts for mobile apps', 'product', 89.00, 'https://readdy.ai/api/search-image?query=mobile%20app%20UI%20kit%20design%20with%20modern%20interface%20elements%20clean%20layouts%20smartphone%20mockups%20professional%20design%20system%20white%20background&width=300&height=200&seq=recent1&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'design'), 'UIExpert', 4.6, 34, false, ARRAY['ui-kit', 'mobile', 'design-system']),

('SEO Optimization Service', 'Complete SEO audit and optimization for better search rankings', 'service', 199.00, 'https://readdy.ai/api/search-image?query=SEO%20optimization%20analytics%20dashboard%20with%20graphs%20charts%20and%20metrics%20professional%20marketing%20interface%20clean%20modern%20design%20white%20background&width=300&height=200&seq=recent2&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'marketing'), 'SEOGuru', 4.8, 67, false, ARRAY['seo', 'optimization', 'analytics']),

('Email Marketing Automation', 'Automated email campaign setup with analytics tracking', 'service', 29.00, 'https://readdy.ai/api/search-image?query=email%20marketing%20automation%20dashboard%20with%20campaign%20metrics%20clean%20interface%20modern%20design%20professional%20marketing%20tools%20white%20background&width=300&height=200&seq=recent3&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'marketing'), 'MarketPro', 4.5, 156, false, ARRAY['email-marketing', 'automation', 'campaigns']),

-- Digital Products
('E-commerce Template Bundle', 'Complete e-commerce website templates with shopping cart functionality', 'product', 149.00, 'https://readdy.ai/api/search-image?query=e-commerce%20website%20template%20bundle%20with%20modern%20clean%20design%20shopping%20cart%20layouts%20product%20pages%20professional%20web%20templates%20white%20background&width=350&height=250&seq=digital1&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'digital-products'), 'TemplateKing', 4.7, 92, false, ARRAY['e-commerce', 'template', 'bundle']),

('Stock Photo Collection', 'High-quality professional stock photos for business use', 'product', 79.00, 'https://readdy.ai/api/search-image?query=professional%20stock%20photo%20collection%20with%20business%20lifestyle%20images%20high%20quality%20photography%20clean%20composition%20modern%20aesthetic%20white%20background&width=350&height=250&seq=digital2&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'digital-products'), 'PhotoPro', 4.6, 156, false, ARRAY['stock-photos', 'photography', 'business']),

('Icon Pack Premium', 'Premium collection of modern minimalist icons for UI design', 'product', 39.00, 'https://readdy.ai/api/search-image?query=premium%20icon%20pack%20collection%20with%20modern%20minimalist%20icons%20clean%20design%20professional%20UI%20elements%20vector%20graphics%20white%20background&width=350&height=250&seq=digital3&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'digital-products'), 'IconMaster', 4.8, 203, false, ARRAY['icons', 'ui-elements', 'vector']),

-- Services
('Digital Marketing Strategy', 'Comprehensive digital marketing strategy with implementation plan', 'service', 499.00, 'https://readdy.ai/api/search-image?query=digital%20marketing%20strategy%20presentation%20with%20charts%20graphs%20analytics%20professional%20business%20meeting%20modern%20office%20environment%20clean%20design&width=350&height=250&seq=service1&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'marketing'), 'MarketingPro', 4.8, 67, false, ARRAY['strategy', 'digital-marketing', 'consulting']),

('Content Writing Service', 'Professional content writing for websites, blogs, and marketing materials', 'service', 129.00, 'https://readdy.ai/api/search-image?query=professional%20content%20writing%20workspace%20with%20laptop%20documents%20modern%20office%20setup%20clean%20minimalist%20environment%20writing%20tools&width=350&height=250&seq=service2&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'marketing'), 'WriteWell', 4.7, 134, false, ARRAY['content-writing', 'copywriting', 'marketing']),

('Video Editing Service', 'Professional video editing with motion graphics and color correction', 'service', 299.00, 'https://readdy.ai/api/search-image?query=professional%20video%20editing%20workspace%20with%20multiple%20monitors%20editing%20software%20timeline%20modern%20studio%20setup%20clean%20professional%20environment&width=350&height=250&seq=service3&orientation=landscape', (SELECT id FROM category_ids WHERE slug = 'design'), 'VideoEdit', 4.9, 89, false, ARRAY['video-editing', 'motion-graphics', 'post-production']);

-- Insert sample orders for dashboard demo
INSERT INTO public.orders (listing_id, buyer_name, seller_name, amount, status, delivery_date, download_urls, created_at) VALUES
((SELECT id FROM public.listings WHERE title = 'Complete Brand Identity Package'), 'John Doe', 'DesignPro', 299.00, 'completed', '2024-01-15 10:00:00+00', ARRAY['https://example.com/brand-package.zip'], '2024-01-15 10:00:00+00'),
((SELECT id FROM public.listings WHERE title = 'Full-Stack Web Development'), 'John Doe', 'CodeMaster', 1299.00, 'in-progress', NULL, NULL, '2024-01-10 10:00:00+00'),
((SELECT id FROM public.listings WHERE title = 'SEO Optimization Service'), 'John Doe', 'SEOGuru', 199.00, 'active', '2024-02-20 10:00:00+00', NULL, '2024-01-20 10:00:00+00');

-- Insert sample downloads
INSERT INTO public.downloads (order_id, user_name, file_name, file_type, file_size, download_url, download_count, last_download) VALUES
((SELECT id FROM public.orders WHERE buyer_name = 'John Doe' AND seller_name = 'DesignPro'), 'John Doe', 'Logo Design Files', 'ZIP Package', 47185920, 'https://example.com/logo-files.zip', 3, '2024-01-16 10:00:00+00'),
((SELECT id FROM public.orders WHERE buyer_name = 'John Doe' AND seller_name = 'DesignPro'), 'John Doe', 'Brand Guidelines PDF', 'PDF Document', 8497152, 'https://example.com/brand-guidelines.pdf', 1, '2024-01-15 10:00:00+00');

-- Insert sample messages
INSERT INTO public.messages (order_id, sender_name, recipient_name, subject, content, read, created_at) VALUES
((SELECT id FROM public.orders WHERE buyer_name = 'John Doe' AND seller_name = 'DesignPro'), 'DesignPro', 'John Doe', 'Logo design revision ready', 'I''ve made the changes you requested for the logo design. Please review and let me know if you need any further adjustments.', true, '2024-01-16 08:00:00+00'),
((SELECT id FROM public.orders WHERE buyer_name = 'John Doe' AND seller_name = 'CodeMaster'), 'CodeMaster', 'John Doe', 'Project milestone update', 'The first phase of development is now complete. I''ve deployed the initial version to the staging server for your review.', false, '2024-01-15 08:00:00+00');